{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,qKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/layout/ModernSidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Home,\n  PieChart,\n  Target,\n  TrendingUp,\n  Settings,\n  HelpCircle,\n  Menu,\n  X,\n  Wallet,\n  BarChart3,\n  Trophy,\n  Bell,\n  User,\n  LogOut,\n} from 'lucide-react';\nimport { useAuth } from '@/context/AuthContext';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Button } from '@/components/ui/button';\nimport { useToast } from '@/hooks/use-toast';\n\ninterface SidebarItem {\n  icon: React.ComponentType<{ className?: string }>;\n  label: string;\n  href: string;\n  badge?: number;\n}\n\nconst sidebarItems: SidebarItem[] = [\n  { icon: Home, label: 'Dashboard', href: '/' },\n  { icon: Wallet, label: 'Income', href: '#' },\n  { icon: PieChart, label: 'Categories', href: '#' },\n  { icon: BarChart3, label: 'Analytics', href: '#' },\n  { icon: Target, label: 'Goals', href: '#' },\n  { icon: Trophy, label: 'Achievements', href: '#', badge: 3 },\n  { icon: TrendingUp, label: 'Reports', href: '#' },\n];\n\nconst bottomItems: SidebarItem[] = [\n  { icon: Bell, label: 'Notifications', href: '#', badge: 2 },\n  { icon: Settings, label: 'Settings', href: '#' },\n  { icon: HelpCircle, label: 'Help', href: '#' },\n];\n\ninterface ModernSidebarProps {\n  isOpen: boolean;\n  onToggle: () => void;\n}\n\nexport default function ModernSidebar({ isOpen, onToggle }: ModernSidebarProps) {\n  const pathname = usePathname();\n  const { currentUser, signOut } = useAuth();\n  const { toast } = useToast();\n\n  const getInitials = (email?: string | null, name?: string | null) => {\n    if (name) {\n      const nameParts = name.split(' ').filter(Boolean);\n      if (nameParts.length > 1) {\n        return (nameParts[0][0] + nameParts[1][0]).toUpperCase();\n      }\n      return name.substring(0, 2).toUpperCase();\n    }\n    if (email) return email.substring(0, 2).toUpperCase();\n    return 'U';\n  };\n\n  const handleNavClick = (item: SidebarItem) => {\n    if (item.href === '#') {\n      toast({\n        title: \"Coming Soon!\",\n        description: `${item.label} feature is under development and will be available soon.`,\n      });\n    }\n  };\n\n  const SidebarContent = () => (\n    <div className=\"flex flex-col h-full overflow-hidden\">\n      {/* Header */}\n      <div className=\"p-6 border-b border-white/10\">\n        <div className=\"flex items-center gap-3\">\n          <motion.div\n            className=\"w-10 h-10 bg-gradient-to-r from-primary to-accent rounded-xl flex items-center justify-center\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Wallet className=\"w-5 h-5 text-white\" />\n          </motion.div>\n          <div>\n            <h1 className=\"text-xl font-bold text-foreground\">BudgetWise</h1>\n            <p className=\"text-xs text-muted-foreground\">Financial Freedom</p>\n          </div>\n        </div>\n      </div>\n\n      {/* User Profile */}\n      <div className=\"p-4 border-b border-white/10\">\n        <div className=\"flex items-center gap-3 p-3 rounded-xl bg-white/5 hover:bg-white/10 transition-colors cursor-pointer\">\n          <Avatar className=\"w-10 h-10\">\n            <AvatarImage src={currentUser?.photoURL || undefined} />\n            <AvatarFallback className=\"bg-primary/20 text-primary\">\n              {getInitials(currentUser?.email, currentUser?.displayName)}\n            </AvatarFallback>\n          </Avatar>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-medium text-foreground truncate\">\n              {currentUser?.displayName || currentUser?.email?.split('@')[0] || 'User'}\n            </p>\n            <p className=\"text-xs text-muted-foreground truncate\">\n              {currentUser?.email}\n            </p>\n          </div>\n          <User className=\"w-4 h-4 text-muted-foreground\" />\n        </div>\n      </div>\n\n      {/* Navigation Items */}\n      <nav className=\"flex-1 p-4 space-y-2 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent\">\n        {sidebarItems.map((item) => {\n          const isActive = pathname === item.href;\n          const Icon = item.icon;\n\n          return (\n            <motion.div\n              key={item.href}\n              whileHover={{ x: 4 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              {item.href === '#' ? (\n                <div\n                  onClick={() => handleNavClick(item)}\n                  className={`${\n                    isActive ? 'sidebar-item-active' : 'sidebar-item'\n                  } relative`}\n                >\n                  <Icon className={`w-5 h-5 ${isActive ? 'text-primary' : 'text-muted-foreground hover:text-foreground'} transition-colors`} />\n                  <span className={`font-medium ${isActive ? 'text-primary' : 'text-foreground'}`}>\n                    {item.label}\n                  </span>\n                  {item.badge && (\n                    <motion.div\n                      className=\"ml-auto w-5 h-5 bg-accent text-accent-foreground rounded-full flex items-center justify-center text-xs font-bold\"\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      transition={{ delay: 0.2 }}\n                    >\n                      {item.badge}\n                    </motion.div>\n                  )}\n                  {isActive && (\n                    <motion.div\n                      className=\"absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r-full\"\n                      layoutId=\"activeIndicator\"\n                      transition={{ type: 'spring', stiffness: 300, damping: 30 }}\n                    />\n                  )}\n                </div>\n              ) : (\n                <Link\n                  href={item.href}\n                  className={`${\n                    isActive ? 'sidebar-item-active' : 'sidebar-item'\n                  } relative`}\n                >\n                  <Icon className={`w-5 h-5 ${isActive ? 'text-primary' : 'text-muted-foreground hover:text-foreground'} transition-colors`} />\n                  <span className={`font-medium ${isActive ? 'text-primary' : 'text-foreground'}`}>\n                    {item.label}\n                  </span>\n                  {item.badge && (\n                    <motion.div\n                      className=\"ml-auto w-5 h-5 bg-accent text-accent-foreground rounded-full flex items-center justify-center text-xs font-bold\"\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      transition={{ delay: 0.2 }}\n                    >\n                      {item.badge}\n                    </motion.div>\n                  )}\n                  {isActive && (\n                    <motion.div\n                      className=\"absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r-full\"\n                      layoutId=\"activeIndicator\"\n                      transition={{ type: 'spring', stiffness: 300, damping: 30 }}\n                    />\n                  )}\n                </Link>\n              )}\n            </motion.div>\n          );\n        })}\n      </nav>\n\n      {/* Bottom Items */}\n      <div className=\"p-4 border-t border-white/10 space-y-2 flex-shrink-0\">\n        {bottomItems.map((item) => {\n          const isActive = pathname === item.href;\n          const Icon = item.icon;\n\n          return (\n            <motion.div\n              key={item.href}\n              whileHover={{ x: 4 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              {item.href === '#' ? (\n                <div\n                  onClick={() => handleNavClick(item)}\n                  className={`${\n                    isActive ? 'sidebar-item-active' : 'sidebar-item'\n                  } relative`}\n                >\n                  <Icon className={`w-5 h-5 ${isActive ? 'text-primary' : 'text-muted-foreground hover:text-foreground'} transition-colors`} />\n                  <span className={`font-medium ${isActive ? 'text-primary' : 'text-foreground'}`}>\n                    {item.label}\n                  </span>\n                  {item.badge && (\n                    <motion.div\n                      className=\"ml-auto w-5 h-5 bg-accent text-accent-foreground rounded-full flex items-center justify-center text-xs font-bold\"\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      transition={{ delay: 0.2 }}\n                    >\n                      {item.badge}\n                    </motion.div>\n                  )}\n                </div>\n              ) : (\n                <Link\n                  href={item.href}\n                  className={`${\n                    isActive ? 'sidebar-item-active' : 'sidebar-item'\n                  } relative`}\n                >\n                  <Icon className={`w-5 h-5 ${isActive ? 'text-primary' : 'text-muted-foreground hover:text-foreground'} transition-colors`} />\n                  <span className={`font-medium ${isActive ? 'text-primary' : 'text-foreground'}`}>\n                    {item.label}\n                  </span>\n                  {item.badge && (\n                    <motion.div\n                      className=\"ml-auto w-5 h-5 bg-accent text-accent-foreground rounded-full flex items-center justify-center text-xs font-bold\"\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      transition={{ delay: 0.2 }}\n                    >\n                      {item.badge}\n                    </motion.div>\n                  )}\n                </Link>\n              )}\n            </motion.div>\n          );\n        })}\n\n        {/* Sign Out */}\n        <motion.div\n          whileHover={{ x: 4 }}\n          whileTap={{ scale: 0.98 }}\n        >\n          <button\n            onClick={signOut}\n            className=\"sidebar-item w-full text-left text-destructive hover:bg-destructive/10\"\n          >\n            <LogOut className=\"w-5 h-5\" />\n            <span className=\"font-medium\">Sign Out</span>\n          </button>\n        </motion.div>\n      </div>\n    </div>\n  );\n\n  return (\n    <>\n      {/* Mobile Toggle Button */}\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        onClick={onToggle}\n        className=\"fixed top-4 left-4 z-50 lg:hidden glass-card w-10 h-10\"\n      >\n        {isOpen ? <X className=\"w-5 h-5\" /> : <Menu className=\"w-5 h-5\" />}\n      </Button>\n\n      {/* Desktop Sidebar */}\n      <motion.aside\n        className=\"hidden lg:flex fixed left-0 top-0 h-full w-80 sidebar-modern z-40\"\n        initial={{ x: -320 }}\n        animate={{ x: 0 }}\n        transition={{ type: 'spring', stiffness: 300, damping: 30 }}\n      >\n        <SidebarContent />\n      </motion.aside>\n\n      {/* Mobile Sidebar */}\n      <AnimatePresence>\n        {isOpen && (\n          <>\n            {/* Backdrop */}\n            <motion.div\n              className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              onClick={onToggle}\n            />\n\n            {/* Sidebar */}\n            <motion.aside\n              className=\"fixed left-0 top-0 h-full w-80 sidebar-modern z-50 lg:hidden\"\n              initial={{ x: -320 }}\n              animate={{ x: 0 }}\n              exit={{ x: -320 }}\n              transition={{ type: 'spring', stiffness: 300, damping: 30 }}\n            >\n              <SidebarContent />\n            </motion.aside>\n          </>\n        )}\n      </AnimatePresence>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;;;AAzBA;;;;;;;;;AAkCA,MAAM,eAA8B;IAClC;QAAE,MAAM,sMAAA,CAAA,OAAI;QAAE,OAAO;QAAa,MAAM;IAAI;IAC5C;QAAE,MAAM,yMAAA,CAAA,SAAM;QAAE,OAAO;QAAU,MAAM;IAAI;IAC3C;QAAE,MAAM,iNAAA,CAAA,WAAQ;QAAE,OAAO;QAAc,MAAM;IAAI;IACjD;QAAE,MAAM,qNAAA,CAAA,YAAS;QAAE,OAAO;QAAa,MAAM;IAAI;IACjD;QAAE,MAAM,yMAAA,CAAA,SAAM;QAAE,OAAO;QAAS,MAAM;IAAI;IAC1C;QAAE,MAAM,yMAAA,CAAA,SAAM;QAAE,OAAO;QAAgB,MAAM;QAAK,OAAO;IAAE;IAC3D;QAAE,MAAM,qNAAA,CAAA,aAAU;QAAE,OAAO;QAAW,MAAM;IAAI;CACjD;AAED,MAAM,cAA6B;IACjC;QAAE,MAAM,qMAAA,CAAA,OAAI;QAAE,OAAO;QAAiB,MAAM;QAAK,OAAO;IAAE;IAC1D;QAAE,MAAM,6MAAA,CAAA,WAAQ;QAAE,OAAO;QAAY,MAAM;IAAI;IAC/C;QAAE,MAAM,qNAAA,CAAA,aAAU;QAAE,OAAO;QAAQ,MAAM;IAAI;CAC9C;AAOc,SAAS,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAsB;;IAC5E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACvC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,cAAc,CAAC,OAAuB;QAC1C,IAAI,MAAM;YACR,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC;YACzC,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW;YACxD;YACA,OAAO,KAAK,SAAS,CAAC,GAAG,GAAG,WAAW;QACzC;QACA,IAAI,OAAO,OAAO,MAAM,SAAS,CAAC,GAAG,GAAG,WAAW;QACnD,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,IAAI,KAAK,KAAK;YACrB,MAAM;gBACJ,OAAO;gBACP,aAAa,GAAG,KAAK,KAAK,CAAC,yDAAyD,CAAC;YACvF;QACF;IACF;IAEA,MAAM,iBAAiB,kBACrB,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;8BAMnD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,6LAAC,qIAAA,CAAA,cAAW;wCAAC,KAAK,aAAa,YAAY;;;;;;kDAC3C,6LAAC,qIAAA,CAAA,iBAAc;wCAAC,WAAU;kDACvB,YAAY,aAAa,OAAO,aAAa;;;;;;;;;;;;0CAGlD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDACV,aAAa,eAAe,aAAa,OAAO,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;kDAEpE,6LAAC;wCAAE,WAAU;kDACV,aAAa;;;;;;;;;;;;0CAGlB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKpB,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC;wBACjB,MAAM,WAAW,aAAa,KAAK,IAAI;wBACvC,MAAM,OAAO,KAAK,IAAI;wBAEtB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,YAAY;gCAAE,GAAG;4BAAE;4BACnB,UAAU;gCAAE,OAAO;4BAAK;sCAEvB,KAAK,IAAI,KAAK,oBACb,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAW,GACT,WAAW,wBAAwB,eACpC,SAAS,CAAC;;kDAEX,6LAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,iBAAiB,8CAA8C,kBAAkB,CAAC;;;;;;kDACzH,6LAAC;wCAAK,WAAW,CAAC,YAAY,EAAE,WAAW,iBAAiB,mBAAmB;kDAC5E,KAAK,KAAK;;;;;;oCAEZ,KAAK,KAAK,kBACT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,OAAO;wCAAE;wCACpB,SAAS;4CAAE,OAAO;wCAAE;wCACpB,YAAY;4CAAE,OAAO;wCAAI;kDAExB,KAAK,KAAK;;;;;;oCAGd,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,UAAS;wCACT,YAAY;4CAAE,MAAM;4CAAU,WAAW;4CAAK,SAAS;wCAAG;;;;;;;;;;;qDAKhE,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,GACT,WAAW,wBAAwB,eACpC,SAAS,CAAC;;kDAEX,6LAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,iBAAiB,8CAA8C,kBAAkB,CAAC;;;;;;kDACzH,6LAAC;wCAAK,WAAW,CAAC,YAAY,EAAE,WAAW,iBAAiB,mBAAmB;kDAC5E,KAAK,KAAK;;;;;;oCAEZ,KAAK,KAAK,kBACT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,OAAO;wCAAE;wCACpB,SAAS;4CAAE,OAAO;wCAAE;wCACpB,YAAY;4CAAE,OAAO;wCAAI;kDAExB,KAAK,KAAK;;;;;;oCAGd,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,UAAS;wCACT,YAAY;4CAAE,MAAM;4CAAU,WAAW;4CAAK,SAAS;wCAAG;;;;;;;;;;;;2BA1D7D,KAAK,IAAI;;;;;oBAiEpB;;;;;;8BAIF,6LAAC;oBAAI,WAAU;;wBACZ,YAAY,GAAG,CAAC,CAAC;4BAChB,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,MAAM,OAAO,KAAK,IAAI;4BAEtB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,YAAY;oCAAE,GAAG;gCAAE;gCACnB,UAAU;oCAAE,OAAO;gCAAK;0CAEvB,KAAK,IAAI,KAAK,oBACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAW,GACT,WAAW,wBAAwB,eACpC,SAAS,CAAC;;sDAEX,6LAAC;4CAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,iBAAiB,8CAA8C,kBAAkB,CAAC;;;;;;sDACzH,6LAAC;4CAAK,WAAW,CAAC,YAAY,EAAE,WAAW,iBAAiB,mBAAmB;sDAC5E,KAAK,KAAK;;;;;;wCAEZ,KAAK,KAAK,kBACT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,YAAY;gDAAE,OAAO;4CAAI;sDAExB,KAAK,KAAK;;;;;;;;;;;yDAKjB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,WAAW,GACT,WAAW,wBAAwB,eACpC,SAAS,CAAC;;sDAEX,6LAAC;4CAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,iBAAiB,8CAA8C,kBAAkB,CAAC;;;;;;sDACzH,6LAAC;4CAAK,WAAW,CAAC,YAAY,EAAE,WAAW,iBAAiB,mBAAmB;sDAC5E,KAAK,KAAK;;;;;;wCAEZ,KAAK,KAAK,kBACT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,YAAY;gDAAE,OAAO;4CAAI;sDAExB,KAAK,KAAK;;;;;;;;;;;;+BA5Cd,KAAK,IAAI;;;;;wBAmDpB;sCAGA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,GAAG;4BAAE;4BACnB,UAAU;gCAAE,OAAO;4BAAK;sCAExB,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOxC,qBACE;;0BAEE,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS;gBACT,WAAU;0BAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;yCAAe,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;0BAIxD,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;gBACX,WAAU;gBACV,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;gBAAG;0BAE1D,cAAA,6LAAC;;;;;;;;;;0BAIH,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC;;sCAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,SAAS;;;;;;sCAIX,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;4BACX,WAAU;4BACV,SAAS;gCAAE,GAAG,CAAC;4BAAI;4BACnB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG,CAAC;4BAAI;4BAChB,YAAY;gCAAE,MAAM;gCAAU,WAAW;gCAAK,SAAS;4BAAG;sCAE1D,cAAA,6LAAC;;;;;;;;;;;;;;;;;;;AAOf;GA9QwB;;QACL,qIAAA,CAAA,cAAW;QACK,iIAAA,CAAA,UAAO;QACtB,+HAAA,CAAA,WAAQ;;;KAHJ", "debugId": null}}, {"offset": {"line": 796, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,+KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,+KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,+KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,+KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,+KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,+KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,+KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qSACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,+KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,6LAAC,+KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,+KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,+KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 1108, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/layout/ModernTopbar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  Search,\n  Bell,\n  Settings,\n  Eye,\n  EyeOff,\n  Plus,\n  Filter,\n  Calendar,\n  Download,\n} from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Badge } from '@/components/ui/badge';\n\ninterface ModernTopbarProps {\n  onMenuClick: () => void;\n  balancesVisible?: boolean;\n  onToggleBalances?: () => void;\n}\n\nexport default function ModernTopbar({\n  onMenuClick,\n  balancesVisible = true,\n  onToggleBalances,\n}: ModernTopbarProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n\n  return (\n    <motion.header\n      className=\"nav-modern sticky top-0 z-30 px-6 py-4\"\n      initial={{ y: -100, opacity: 0 }}\n      animate={{ y: 0, opacity: 1 }}\n      transition={{ duration: 0.5, ease: 'easeOut' }}\n    >\n      <div className=\"flex items-center justify-between\">\n        {/* Left Section */}\n        <div className=\"flex items-center gap-4\">\n          {/* Search */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Search transactions, categories...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"input-modern pl-10 w-80 hidden md:block\"\n            />\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"hidden lg:flex items-center gap-2\">\n            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n              <Button variant=\"ghost\" size=\"sm\" className=\"glass-card\">\n                <Filter className=\"w-4 h-4 mr-2\" />\n                Filter\n              </Button>\n            </motion.div>\n            \n            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n              <Button variant=\"ghost\" size=\"sm\" className=\"glass-card\">\n                <Calendar className=\"w-4 h-4 mr-2\" />\n                This Month\n              </Button>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Right Section */}\n        <div className=\"flex items-center gap-3\">\n          {/* Balance Visibility Toggle */}\n          {onToggleBalances && (\n            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={onToggleBalances}\n                className=\"glass-card w-10 h-10\"\n              >\n                <motion.div\n                  key={balancesVisible ? 'visible' : 'hidden'}\n                  initial={{ rotate: 180, opacity: 0 }}\n                  animate={{ rotate: 0, opacity: 1 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  {balancesVisible ? (\n                    <Eye className=\"w-4 h-4\" />\n                  ) : (\n                    <EyeOff className=\"w-4 h-4\" />\n                  )}\n                </motion.div>\n              </Button>\n            </motion.div>\n          )}\n\n          {/* Export Button */}\n          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n            <Button variant=\"ghost\" size=\"icon\" className=\"glass-card w-10 h-10\">\n              <Download className=\"w-4 h-4\" />\n            </Button>\n          </motion.div>\n\n          {/* Notifications */}\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n                <Button variant=\"ghost\" size=\"icon\" className=\"glass-card w-10 h-10 relative\">\n                  <Bell className=\"w-4 h-4\" />\n                  <motion.div\n                    className=\"absolute -top-1 -right-1 w-3 h-3 bg-accent rounded-full\"\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    transition={{ delay: 0.2 }}\n                  />\n                </Button>\n              </motion.div>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\" className=\"w-80 glass-card border-white/20\">\n              <DropdownMenuLabel className=\"flex items-center justify-between\">\n                Notifications\n                <Badge variant=\"secondary\" className=\"badge-modern\">\n                  3 new\n                </Badge>\n              </DropdownMenuLabel>\n              <DropdownMenuSeparator className=\"bg-white/10\" />\n              <DropdownMenuItem className=\"p-4 hover:bg-white/10\">\n                <div className=\"space-y-1\">\n                  <p className=\"text-sm font-medium\">Budget Goal Achieved!</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    You've successfully saved R1,500 this month\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">2 hours ago</p>\n                </div>\n              </DropdownMenuItem>\n              <DropdownMenuItem className=\"p-4 hover:bg-white/10\">\n                <div className=\"space-y-1\">\n                  <p className=\"text-sm font-medium\">Overspending Alert</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    You're 15% over budget in Entertainment category\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">1 day ago</p>\n                </div>\n              </DropdownMenuItem>\n              <DropdownMenuItem className=\"p-4 hover:bg-white/10\">\n                <div className=\"space-y-1\">\n                  <p className=\"text-sm font-medium\">New Achievement Unlocked</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    \"Savings Streak\" - 7 days of staying under budget\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">3 days ago</p>\n                </div>\n              </DropdownMenuItem>\n            </DropdownMenuContent>\n          </DropdownMenu>\n\n          {/* Add New Button */}\n          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n            <Button className=\"btn-modern-primary hidden sm:flex\">\n              <Plus className=\"w-4 h-4 mr-2\" />\n              Add Transaction\n            </Button>\n          </motion.div>\n\n          {/* Mobile Add Button */}\n          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n            <Button size=\"icon\" className=\"btn-modern-primary sm:hidden\">\n              <Plus className=\"w-4 h-4\" />\n            </Button>\n          </motion.div>\n\n          {/* Settings */}\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n                <Button variant=\"ghost\" size=\"icon\" className=\"glass-card w-10 h-10\">\n                  <Settings className=\"w-4 h-4\" />\n                </Button>\n              </motion.div>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\" className=\"glass-card border-white/20\">\n              <DropdownMenuLabel>Quick Settings</DropdownMenuLabel>\n              <DropdownMenuSeparator className=\"bg-white/10\" />\n              <DropdownMenuItem className=\"hover:bg-white/10\">\n                <span>Dark Mode</span>\n              </DropdownMenuItem>\n              <DropdownMenuItem className=\"hover:bg-white/10\">\n                <span>Currency Settings</span>\n              </DropdownMenuItem>\n              <DropdownMenuItem className=\"hover:bg-white/10\">\n                <span>Export Data</span>\n              </DropdownMenuItem>\n              <DropdownMenuSeparator className=\"bg-white/10\" />\n              <DropdownMenuItem className=\"hover:bg-white/10\">\n                <span>Preferences</span>\n              </DropdownMenuItem>\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n      </div>\n\n      {/* Mobile Search */}\n      <motion.div\n        className=\"mt-4 md:hidden\"\n        initial={{ opacity: 0, height: 0 }}\n        animate={{ opacity: 1, height: 'auto' }}\n        transition={{ delay: 0.2 }}\n      >\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground\" />\n          <Input\n            placeholder=\"Search...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"input-modern pl-10 w-full\"\n          />\n        </div>\n      </motion.div>\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AAQA;;;AAzBA;;;;;;;;AAiCe,SAAS,aAAa,EACnC,WAAW,EACX,kBAAkB,IAAI,EACtB,gBAAgB,EACE;;IAClB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAU;QACV,SAAS;YAAE,GAAG,CAAC;YAAK,SAAS;QAAE;QAC/B,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;;0BAE7C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAAC,YAAY;4CAAE,OAAO;wCAAK;wCAAG,UAAU;4CAAE,OAAO;wCAAK;kDAC/D,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;;8DAC1C,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAKvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAAC,YAAY;4CAAE,OAAO;wCAAK;wCAAG,UAAU;4CAAE,OAAO;wCAAK;kDAC/D,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;;8DAC1C,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAQ7C,6LAAC;wBAAI,WAAU;;4BAEZ,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,YAAY;oCAAE,OAAO;gCAAK;gCAAG,UAAU;oCAAE,OAAO;gCAAK;0CAC/D,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,QAAQ;4CAAK,SAAS;wCAAE;wCACnC,SAAS;4CAAE,QAAQ;4CAAG,SAAS;wCAAE;wCACjC,YAAY;4CAAE,UAAU;wCAAI;kDAE3B,gCACC,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;iEAEf,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;uCARf,kBAAkB,YAAY;;;;;;;;;;;;;;;0CAgB3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,YAAY;oCAAE,OAAO;gCAAK;gCAAG,UAAU;oCAAE,OAAO;gCAAK;0CAC/D,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;8CAC5C,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAKxB,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAAC,YAAY;gDAAE,OAAO;4CAAK;4CAAG,UAAU;gDAAE,OAAO;4CAAK;sDAC/D,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAO,WAAU;;kEAC5C,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,OAAO;wDAAE;wDACpB,SAAS;4DAAE,OAAO;wDAAE;wDACpB,YAAY;4DAAE,OAAO;wDAAI;;;;;;;;;;;;;;;;;;;;;;kDAKjC,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,6LAAC,+IAAA,CAAA,oBAAiB;gDAAC,WAAU;;oDAAoC;kEAE/D,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAAe;;;;;;;;;;;;0DAItD,6LAAC,+IAAA,CAAA,wBAAqB;gDAAC,WAAU;;;;;;0DACjC,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,WAAU;0DAC1B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAsB;;;;;;sEACnC,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;sEAG7C,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;0DAGjD,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,WAAU;0DAC1B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAsB;;;;;;sEACnC,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;sEAG7C,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;0DAGjD,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,WAAU;0DAC1B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAsB;;;;;;sEACnC,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;sEAG7C,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOrD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,YAAY;oCAAE,OAAO;gCAAK;gCAAG,UAAU;oCAAE,OAAO;gCAAK;0CAC/D,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAMrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,YAAY;oCAAE,OAAO;gCAAK;gCAAG,UAAU;oCAAE,OAAO;gCAAK;0CAC/D,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAO,WAAU;8CAC5B,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAKpB,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAAC,YAAY;gDAAE,OAAO;4CAAK;4CAAG,UAAU;gDAAE,OAAO;4CAAK;sDAC/D,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAO,WAAU;0DAC5C,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;kDAI1B,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,6LAAC,+IAAA,CAAA,oBAAiB;0DAAC;;;;;;0DACnB,6LAAC,+IAAA,CAAA,wBAAqB;gDAAC,WAAU;;;;;;0DACjC,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,WAAU;0DAC1B,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DAER,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,WAAU;0DAC1B,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DAER,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,WAAU;0DAC1B,cAAA,6LAAC;8DAAK;;;;;;;;;;;0DAER,6LAAC,+IAAA,CAAA,wBAAqB;gDAAC,WAAU;;;;;;0DACjC,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,WAAU;0DAC1B,cAAA,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBACjC,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAO;gBACtC,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC,oIAAA,CAAA,QAAK;4BACJ,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMtB;GArMwB;KAAA", "debugId": null}}, {"offset": {"line": 1826, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/layout/ModernLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, ReactNode } from 'react';\nimport { motion } from 'framer-motion';\nimport ModernSidebar from './ModernSidebar';\nimport ModernTopbar from './ModernTopbar';\n\ninterface ModernLayoutProps {\n  children: ReactNode;\n  balancesVisible?: boolean;\n  onToggleBalances?: () => void;\n}\n\nexport default function ModernLayout({\n  children,\n  balancesVisible = true,\n  onToggleBalances\n}: ModernLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Sidebar */}\n      <ModernSidebar isOpen={sidebarOpen} onToggle={toggleSidebar} />\n\n      {/* Main Content */}\n      <div className=\"lg:ml-80\">\n        {/* Top Bar */}\n        <ModernTopbar\n          onMenuClick={toggleSidebar}\n          balancesVisible={balancesVisible}\n          onToggleBalances={onToggleBalances}\n        />\n\n        {/* Page Content */}\n        <motion.main\n          className=\"p-6 min-h-screen\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, ease: 'easeOut' }}\n        >\n          {children}\n        </motion.main>\n      </div>\n\n      {/* Background Effects */}\n      <div className=\"fixed inset-0 -z-10 overflow-hidden pointer-events-none\">\n        {/* Gradient Orbs */}\n        <motion.div\n          className=\"absolute -top-40 -right-40 w-80 h-80 bg-primary/10 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.3, 0.5, 0.3],\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            ease: 'easeInOut',\n          }}\n        />\n        <motion.div\n          className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-accent/10 rounded-full blur-3xl\"\n          animate={{\n            scale: [1.2, 1, 1.2],\n            opacity: [0.5, 0.3, 0.5],\n          }}\n          transition={{\n            duration: 10,\n            repeat: Infinity,\n            ease: 'easeInOut',\n          }}\n        />\n        <motion.div\n          className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-primary/5 rounded-full blur-3xl\"\n          animate={{\n            rotate: [0, 360],\n            scale: [1, 1.1, 1],\n          }}\n          transition={{\n            duration: 20,\n            repeat: Infinity,\n            ease: 'linear',\n          }}\n        />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAae,SAAS,aAAa,EACnC,QAAQ,EACR,kBAAkB,IAAI,EACtB,gBAAgB,EACE;;IAClB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB,IAAM,eAAe,CAAC;IAE5C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,gJAAA,CAAA,UAAa;gBAAC,QAAQ;gBAAa,UAAU;;;;;;0BAG9C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+IAAA,CAAA,UAAY;wBACX,aAAa;wBACb,iBAAiB;wBACjB,kBAAkB;;;;;;kCAIpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAU;kCAE5C;;;;;;;;;;;;0BAKL,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;wBAC1B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAK;gCAAG;6BAAI;4BACpB,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;wBAC1B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,QAAQ;gCAAC;gCAAG;6BAAI;4BAChB,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;;;;;;;AAKV;GA5EwB;KAAA", "debugId": null}}, {"offset": {"line": 1994, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/animated-wrapper.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, AnimatePresence, Variants } from 'framer-motion';\nimport { ReactNode } from 'react';\n\ninterface AnimatedWrapperProps {\n  children: ReactNode;\n  variant?: 'slideUp' | 'slideLeft' | 'slideRight' | 'scaleIn' | 'fadeIn' | 'stagger';\n  delay?: number;\n  duration?: number;\n  className?: string;\n  staggerChildren?: number;\n}\n\nconst variants: Record<string, Variants> = {\n  slideUp: {\n    hidden: { opacity: 0, y: 30 },\n    visible: { opacity: 1, y: 0 },\n  },\n  slideLeft: {\n    hidden: { opacity: 0, x: -30 },\n    visible: { opacity: 1, x: 0 },\n  },\n  slideRight: {\n    hidden: { opacity: 0, x: 30 },\n    visible: { opacity: 1, x: 0 },\n  },\n  scaleIn: {\n    hidden: { opacity: 0, scale: 0.9 },\n    visible: { opacity: 1, scale: 1 },\n  },\n  fadeIn: {\n    hidden: { opacity: 0 },\n    visible: { opacity: 1 },\n  },\n  stagger: {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  },\n};\n\nexport function AnimatedWrapper({\n  children,\n  variant = 'slideUp',\n  delay = 0,\n  duration = 0.5,\n  className,\n  staggerChildren,\n}: AnimatedWrapperProps) {\n  const motionVariants = variants[variant];\n\n  return (\n    <motion.div\n      className={className}\n      initial=\"hidden\"\n      animate=\"visible\"\n      variants={motionVariants}\n      transition={{\n        duration,\n        delay,\n        ease: 'easeOut',\n        staggerChildren: variant === 'stagger' ? (staggerChildren || 0.1) : undefined,\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\nexport function AnimatedCard({\n  children,\n  className,\n  delay = 0,\n}: {\n  children: ReactNode;\n  className?: string;\n  delay?: number;\n}) {\n  return (\n    <motion.div\n      className={className}\n      initial={{ opacity: 0, y: 20, scale: 0.95 }}\n      animate={{ opacity: 1, y: 0, scale: 1 }}\n      transition={{\n        duration: 0.4,\n        delay,\n        ease: 'easeOut',\n      }}\n      whileHover={{\n        scale: 1.02,\n        transition: { duration: 0.2 },\n      }}\n      whileTap={{ scale: 0.98 }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\nexport function AnimatedButton({\n  children,\n  className,\n  onClick,\n  disabled = false,\n  variant = 'default',\n}: {\n  children: ReactNode;\n  className?: string;\n  onClick?: () => void;\n  disabled?: boolean;\n  variant?: 'default' | 'primary' | 'accent' | 'success';\n}) {\n  return (\n    <motion.button\n      className={className}\n      onClick={onClick}\n      disabled={disabled}\n      initial={{ scale: 1 }}\n      whileHover={!disabled ? { scale: 1.05 } : {}}\n      whileTap={!disabled ? { scale: 0.95 } : {}}\n      transition={{\n        type: 'spring',\n        stiffness: 400,\n        damping: 17,\n      }}\n    >\n      {children}\n    </motion.button>\n  );\n}\n\nexport function AnimatedProgress({\n  value,\n  max = 100,\n  className,\n}: {\n  value: number;\n  max?: number;\n  className?: string;\n}) {\n  const percentage = Math.min((value / max) * 100, 100);\n\n  return (\n    <div className={`w-full bg-muted rounded-full h-2 overflow-hidden ${className}`}>\n      <motion.div\n        className=\"h-full bg-gradient-to-r from-primary to-primary/80 rounded-full\"\n        initial={{ width: 0 }}\n        animate={{ width: `${percentage}%` }}\n        transition={{\n          duration: 1,\n          ease: 'easeOut',\n        }}\n      />\n    </div>\n  );\n}\n\nexport function AnimatedNumber({\n  value,\n  duration = 1,\n  className,\n  prefix = '',\n  suffix = '',\n}: {\n  value: number;\n  duration?: number;\n  className?: string;\n  prefix?: string;\n  suffix?: string;\n}) {\n  return (\n    <motion.span\n      className={className}\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      <motion.span\n        initial={{ scale: 1.2 }}\n        animate={{ scale: 1 }}\n        transition={{ duration: 0.3, ease: 'easeOut' }}\n      >\n        {prefix}\n        <motion.span\n          key={value}\n          initial={{ y: 20, opacity: 0 }}\n          animate={{ y: 0, opacity: 1 }}\n          transition={{ duration: 0.3, ease: 'easeOut' }}\n        >\n          {value.toFixed(2)}\n        </motion.span>\n        {suffix}\n      </motion.span>\n    </motion.span>\n  );\n}\n\nexport function AnimatedList({\n  children,\n  className,\n  staggerDelay = 0.1,\n}: {\n  children: ReactNode;\n  className?: string;\n  staggerDelay?: number;\n}) {\n  return (\n    <motion.div\n      className={className}\n      initial=\"hidden\"\n      animate=\"visible\"\n      variants={{\n        hidden: { opacity: 0 },\n        visible: {\n          opacity: 1,\n          transition: {\n            staggerChildren: staggerDelay,\n          },\n        },\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\nexport function AnimatedListItem({\n  children,\n  className,\n}: {\n  children: ReactNode;\n  className?: string;\n}) {\n  return (\n    <motion.div\n      className={className}\n      variants={{\n        hidden: { opacity: 0, x: -20 },\n        visible: { opacity: 1, x: 0 },\n      }}\n      transition={{ duration: 0.3, ease: 'easeOut' }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\nexport function FloatingElement({\n  children,\n  className,\n  intensity = 10,\n}: {\n  children: ReactNode;\n  className?: string;\n  intensity?: number;\n}) {\n  return (\n    <motion.div\n      className={className}\n      animate={{\n        y: [-intensity, intensity, -intensity],\n      }}\n      transition={{\n        duration: 3,\n        repeat: Infinity,\n        ease: 'easeInOut',\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAFA;;;AAcA,MAAM,WAAqC;IACzC,SAAS;QACP,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,WAAW;QACT,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,YAAY;QACV,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,SAAS;QACP,QAAQ;YAAE,SAAS;YAAG,OAAO;QAAI;QACjC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;IAClC;IACA,QAAQ;QACN,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YAAE,SAAS;QAAE;IACxB;IACA,SAAS;QACP,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;AACF;AAEO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,UAAU,SAAS,EACnB,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,SAAS,EACT,eAAe,EACM;IACrB,MAAM,iBAAiB,QAAQ,CAAC,QAAQ;IAExC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,SAAQ;QACR,SAAQ;QACR,UAAU;QACV,YAAY;YACV;YACA;YACA,MAAM;YACN,iBAAiB,YAAY,YAAa,mBAAmB,MAAO;QACtE;kBAEC;;;;;;AAGP;KA1BgB;AA4BT,SAAS,aAAa,EAC3B,QAAQ,EACR,SAAS,EACT,QAAQ,CAAC,EAKV;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,SAAS;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAK;QAC1C,SAAS;YAAE,SAAS;YAAG,GAAG;YAAG,OAAO;QAAE;QACtC,YAAY;YACV,UAAU;YACV;YACA,MAAM;QACR;QACA,YAAY;YACV,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,UAAU;YAAE,OAAO;QAAK;kBAEvB;;;;;;AAGP;MA5BgB;AA8BT,SAAS,eAAe,EAC7B,QAAQ,EACR,SAAS,EACT,OAAO,EACP,WAAW,KAAK,EAChB,UAAU,SAAS,EAOpB;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW;QACX,SAAS;QACT,UAAU;QACV,SAAS;YAAE,OAAO;QAAE;QACpB,YAAY,CAAC,WAAW;YAAE,OAAO;QAAK,IAAI,CAAC;QAC3C,UAAU,CAAC,WAAW;YAAE,OAAO;QAAK,IAAI,CAAC;QACzC,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;kBAEC;;;;;;AAGP;MA9BgB;AAgCT,SAAS,iBAAiB,EAC/B,KAAK,EACL,MAAM,GAAG,EACT,SAAS,EAKV;IACC,MAAM,aAAa,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK;IAEjD,qBACE,6LAAC;QAAI,WAAW,CAAC,iDAAiD,EAAE,WAAW;kBAC7E,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,OAAO;YAAE;YACpB,SAAS;gBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;YAAC;YACnC,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;;;;;;;;;;;AAIR;MAxBgB;AA0BT,SAAS,eAAe,EAC7B,KAAK,EACL,WAAW,CAAC,EACZ,SAAS,EACT,SAAS,EAAE,EACX,SAAS,EAAE,EAOZ;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;QACV,WAAW;QACX,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;YACV,SAAS;gBAAE,OAAO;YAAI;YACtB,SAAS;gBAAE,OAAO;YAAE;YACpB,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;;gBAE5C;8BACD,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oBAEV,SAAS;wBAAE,GAAG;wBAAI,SAAS;oBAAE;oBAC7B,SAAS;wBAAE,GAAG;wBAAG,SAAS;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;8BAE5C,MAAM,OAAO,CAAC;mBALV;;;;;gBAON;;;;;;;;;;;;AAIT;MAtCgB;AAwCT,SAAS,aAAa,EAC3B,QAAQ,EACR,SAAS,EACT,eAAe,GAAG,EAKnB;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,SAAQ;QACR,SAAQ;QACR,UAAU;YACR,QAAQ;gBAAE,SAAS;YAAE;YACrB,SAAS;gBACP,SAAS;gBACT,YAAY;oBACV,iBAAiB;gBACnB;YACF;QACF;kBAEC;;;;;;AAGP;MA3BgB;AA6BT,SAAS,iBAAiB,EAC/B,QAAQ,EACR,SAAS,EAIV;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;YACR,QAAQ;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;QAC9B;QACA,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE5C;;;;;;AAGP;MAnBgB;AAqBT,SAAS,gBAAgB,EAC9B,QAAQ,EACR,SAAS,EACT,YAAY,EAAE,EAKf;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,SAAS;YACP,GAAG;gBAAC,CAAC;gBAAW;gBAAW,CAAC;aAAU;QACxC;QACA,YAAY;YACV,UAAU;YACV,QAAQ;YACR,MAAM;QACR;kBAEC;;;;;;AAGP;MAxBgB", "debugId": null}}, {"offset": {"line": 2329, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/dashboard/ModernMetricCard.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { motion } from 'framer-motion';\nimport { TrendingUp, TrendingDown, Minus, LucideIcon } from 'lucide-react';\nimport { AnimatedNumber } from '@/components/ui/animated-wrapper';\n\ninterface ModernMetricCardProps {\n  title: string;\n  value: number;\n  previousValue?: number;\n  prefix?: string;\n  suffix?: string;\n  icon: LucideIcon;\n  trend?: 'up' | 'down' | 'neutral';\n  trendValue?: number;\n  description?: string;\n  className?: string;\n  balancesVisible?: boolean;\n  children?: ReactNode;\n}\n\nexport default function ModernMetricCard({\n  title,\n  value,\n  previousValue,\n  prefix = 'R ',\n  suffix = '',\n  icon: Icon,\n  trend,\n  trendValue,\n  description,\n  className = '',\n  balancesVisible = true,\n  children,\n}: ModernMetricCardProps) {\n  const getTrendIcon = () => {\n    switch (trend) {\n      case 'up':\n        return TrendingUp;\n      case 'down':\n        return TrendingDown;\n      default:\n        return Minus;\n    }\n  };\n\n  const getTrendColor = () => {\n    switch (trend) {\n      case 'up':\n        return 'text-success';\n      case 'down':\n        return 'text-destructive';\n      default:\n        return 'text-muted-foreground';\n    }\n  };\n\n  const TrendIcon = getTrendIcon();\n\n  return (\n    <motion.div\n      className={`metric-card glow-primary-modern ${className}`}\n      initial={{ opacity: 0, y: 20, scale: 0.95 }}\n      animate={{ opacity: 1, y: 0, scale: 1 }}\n      transition={{ duration: 0.4, ease: 'easeOut' }}\n      whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}\n    >\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center gap-3\">\n          <motion.div\n            className=\"w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl flex items-center justify-center\"\n            whileHover={{ scale: 1.1, rotate: 5 }}\n            transition={{ type: 'spring', stiffness: 400, damping: 17 }}\n          >\n            <Icon className=\"w-6 h-6 text-primary\" />\n          </motion.div>\n          <div>\n            <h3 className=\"text-sm font-medium text-muted-foreground\">{title}</h3>\n            {description && (\n              <p className=\"text-xs text-muted-foreground/70\">{description}</p>\n            )}\n          </div>\n        </div>\n\n        {/* Trend Indicator */}\n        {trend && trendValue !== undefined && (\n          <motion.div\n            className={`flex items-center gap-1 px-2 py-1 rounded-lg bg-white/5 ${getTrendColor()}`}\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2 }}\n          >\n            <TrendIcon className=\"w-3 h-3\" />\n            <span className=\"text-xs font-medium\">\n              {Math.abs(trendValue).toFixed(1)}%\n            </span>\n          </motion.div>\n        )}\n      </div>\n\n      {/* Value */}\n      <div className=\"mb-4\">\n        <div className={`text-3xl font-bold ${balancesVisible ? '' : 'blur-sm'}`}>\n          <AnimatedNumber\n            value={value}\n            prefix={prefix}\n            suffix={suffix}\n            duration={1}\n          />\n        </div>\n        \n        {previousValue !== undefined && balancesVisible && (\n          <motion.div\n            className=\"flex items-center gap-2 mt-2\"\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3 }}\n          >\n            <span className=\"text-sm text-muted-foreground\">\n              vs {prefix}{previousValue.toFixed(2)}{suffix} last month\n            </span>\n          </motion.div>\n        )}\n      </div>\n\n      {/* Additional Content */}\n      {children && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.4 }}\n        >\n          {children}\n        </motion.div>\n      )}\n\n      {/* Decorative Elements */}\n      <div className=\"absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-primary/10 to-transparent rounded-bl-full\" />\n      <div className=\"absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-accent/10 to-transparent rounded-tr-full\" />\n    </motion.div>\n  );\n}\n\ninterface QuickStatsGridProps {\n  totalIncome: number;\n  totalAllocated: number;\n  remaining: number;\n  savingsGoal?: number;\n  balancesVisible?: boolean;\n}\n\nexport function QuickStatsGrid({\n  totalIncome,\n  totalAllocated,\n  remaining,\n  savingsGoal,\n  balancesVisible = true,\n}: QuickStatsGridProps) {\n  const allocationPercentage = totalIncome > 0 ? (totalAllocated / totalIncome) * 100 : 0;\n  const savingsPercentage = savingsGoal && totalIncome > 0 ? (remaining / savingsGoal) * 100 : 0;\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n      <ModernMetricCard\n        title=\"Total Income\"\n        value={totalIncome}\n        icon={TrendingUp}\n        trend=\"up\"\n        trendValue={12.5}\n        description=\"Monthly income\"\n        balancesVisible={balancesVisible}\n      >\n        <div className=\"progress-modern h-2\">\n          <motion.div\n            className=\"h-full\"\n            initial={{ width: 0 }}\n            animate={{ width: '100%' }}\n            transition={{ duration: 1, delay: 0.5 }}\n          />\n        </div>\n      </ModernMetricCard>\n\n      <ModernMetricCard\n        title=\"Allocated\"\n        value={totalAllocated}\n        icon={TrendingDown}\n        trend={allocationPercentage > 100 ? 'down' : 'up'}\n        trendValue={allocationPercentage}\n        description=\"Budget allocation\"\n        balancesVisible={balancesVisible}\n      >\n        <div className=\"progress-modern h-2\">\n          <motion.div\n            className=\"h-full\"\n            initial={{ width: 0 }}\n            animate={{ width: `${Math.min(allocationPercentage, 100)}%` }}\n            transition={{ duration: 1, delay: 0.5 }}\n          />\n        </div>\n      </ModernMetricCard>\n\n      <ModernMetricCard\n        title=\"Remaining\"\n        value={remaining}\n        icon={remaining >= 0 ? TrendingUp : TrendingDown}\n        trend={remaining >= 0 ? 'up' : 'down'}\n        trendValue={Math.abs((remaining / totalIncome) * 100)}\n        description={remaining >= 0 ? 'Available to save' : 'Over budget'}\n        balancesVisible={balancesVisible}\n        className={remaining < 0 ? 'border-destructive/20' : ''}\n      />\n\n      {savingsGoal && (\n        <ModernMetricCard\n          title=\"Savings Goal\"\n          value={savingsGoal}\n          icon={TrendingUp}\n          trend={savingsPercentage >= 100 ? 'up' : 'neutral'}\n          trendValue={savingsPercentage}\n          description=\"Monthly target\"\n          balancesVisible={balancesVisible}\n        >\n          <div className=\"progress-modern h-2\">\n            <motion.div\n              className=\"h-full bg-gradient-to-r from-success via-success/90 to-success/80\"\n              initial={{ width: 0 }}\n              animate={{ width: `${Math.min(savingsPercentage || 0, 100)}%` }}\n              transition={{ duration: 1, delay: 0.5 }}\n            />\n          </div>\n        </ModernMetricCard>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAAA;AAAA;AACA;AALA;;;;;AAsBe,SAAS,iBAAiB,EACvC,KAAK,EACL,KAAK,EACL,aAAa,EACb,SAAS,IAAI,EACb,SAAS,EAAE,EACX,MAAM,IAAI,EACV,KAAK,EACL,UAAU,EACV,WAAW,EACX,YAAY,EAAE,EACd,kBAAkB,IAAI,EACtB,QAAQ,EACc;IACtB,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO,qNAAA,CAAA,aAAU;YACnB,KAAK;gBACH,OAAO,yNAAA,CAAA,eAAY;YACrB;gBACE,OAAO,uMAAA,CAAA,QAAK;QAChB;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,YAAY;IAElB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,gCAAgC,EAAE,WAAW;QACzD,SAAS;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAK;QAC1C,SAAS;YAAE,SAAS;YAAG,GAAG;YAAG,OAAO;QAAE;QACtC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,YAAY;YAAE,OAAO;YAAM,YAAY;gBAAE,UAAU;YAAI;QAAE;;0BAGzD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;oCAAK,QAAQ;gCAAE;gCACpC,YAAY;oCAAE,MAAM;oCAAU,WAAW;oCAAK,SAAS;gCAAG;0CAE1D,cAAA,6LAAC;oCAAK,WAAU;;;;;;;;;;;0CAElB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6C;;;;;;oCAC1D,6BACC,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;oBAMtD,SAAS,eAAe,2BACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAW,CAAC,wDAAwD,EAAE,iBAAiB;wBACvF,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,6LAAC;gCAAU,WAAU;;;;;;0CACrB,6LAAC;gCAAK,WAAU;;oCACb,KAAK,GAAG,CAAC,YAAY,OAAO,CAAC;oCAAG;;;;;;;;;;;;;;;;;;;0BAOzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,CAAC,mBAAmB,EAAE,kBAAkB,KAAK,WAAW;kCACtE,cAAA,6LAAC,kJAAA,CAAA,iBAAc;4BACb,OAAO;4BACP,QAAQ;4BACR,QAAQ;4BACR,UAAU;;;;;;;;;;;oBAIb,kBAAkB,aAAa,iCAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,6LAAC;4BAAK,WAAU;;gCAAgC;gCAC1C;gCAAQ,cAAc,OAAO,CAAC;gCAAI;gCAAO;;;;;;;;;;;;;;;;;;YAOpD,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;0BAExB;;;;;;0BAKL,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KAzHwB;AAmIjB,SAAS,eAAe,EAC7B,WAAW,EACX,cAAc,EACd,SAAS,EACT,WAAW,EACX,kBAAkB,IAAI,EACF;IACpB,MAAM,uBAAuB,cAAc,IAAI,AAAC,iBAAiB,cAAe,MAAM;IACtF,MAAM,oBAAoB,eAAe,cAAc,IAAI,AAAC,YAAY,cAAe,MAAM;IAE7F,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,OAAM;gBACN,OAAO;gBACP,MAAM,qNAAA,CAAA,aAAU;gBAChB,OAAM;gBACN,YAAY;gBACZ,aAAY;gBACZ,iBAAiB;0BAEjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO;wBAAO;wBACzB,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAI;;;;;;;;;;;;;;;;0BAK5C,6LAAC;gBACC,OAAM;gBACN,OAAO;gBACP,MAAM,yNAAA,CAAA,eAAY;gBAClB,OAAO,uBAAuB,MAAM,SAAS;gBAC7C,YAAY;gBACZ,aAAY;gBACZ,iBAAiB;0BAEjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO,GAAG,KAAK,GAAG,CAAC,sBAAsB,KAAK,CAAC,CAAC;wBAAC;wBAC5D,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAI;;;;;;;;;;;;;;;;0BAK5C,6LAAC;gBACC,OAAM;gBACN,OAAO;gBACP,MAAM,aAAa,IAAI,qNAAA,CAAA,aAAU,GAAG,yNAAA,CAAA,eAAY;gBAChD,OAAO,aAAa,IAAI,OAAO;gBAC/B,YAAY,KAAK,GAAG,CAAC,AAAC,YAAY,cAAe;gBACjD,aAAa,aAAa,IAAI,sBAAsB;gBACpD,iBAAiB;gBACjB,WAAW,YAAY,IAAI,0BAA0B;;;;;;YAGtD,6BACC,6LAAC;gBACC,OAAM;gBACN,OAAO;gBACP,MAAM,qNAAA,CAAA,aAAU;gBAChB,OAAO,qBAAqB,MAAM,OAAO;gBACzC,YAAY;gBACZ,aAAY;gBACZ,iBAAiB;0BAEjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO,GAAG,KAAK,GAAG,CAAC,qBAAqB,GAAG,KAAK,CAAC,CAAC;wBAAC;wBAC9D,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAI;;;;;;;;;;;;;;;;;;;;;;AAOpD;MAnFgB", "debugId": null}}, {"offset": {"line": 2739, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2842, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/accordion.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Accordion = AccordionPrimitive.Root\n\nconst AccordionItem = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <AccordionPrimitive.Item\n    ref={ref}\n    className={cn(\"border-b\", className)}\n    {...props}\n  />\n))\nAccordionItem.displayName = \"AccordionItem\"\n\nconst AccordionTrigger = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Header className=\"flex\">\n    <AccordionPrimitive.Trigger\n      ref={ref}\n      className={cn(\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>.accordion-trigger-chevron]:rotate-180\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200 accordion-trigger-chevron\" />\n    </AccordionPrimitive.Trigger>\n  </AccordionPrimitive.Header>\n))\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\n\nconst AccordionContent = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Content\n    ref={ref}\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n    {...props}\n  >\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\n  </AccordionPrimitive.Content>\n))\n\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,YAAY,wKAAA,CAAA,OAAuB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,wKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,wKAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,6LAAC,wKAAA,CAAA,UAA0B;YACzB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uJACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,iBAAiB,WAAW,GAAG,wKAAA,CAAA,UAA0B,CAAC,WAAW;AAErE,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,wKAAA,CAAA,UAA0B;QACzB,KAAK;QACL,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;;AAIjD,iBAAiB,WAAW,GAAG,wKAAA,CAAA,UAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2935, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3086, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/budget/SubCategoryItem.tsx"], "sourcesContent": ["\n'use client';\n\nimport { SubCategory } from '@/lib/types';\nimport { Button } from '@/components/ui/button';\nimport { Edit3, Trash2 } from 'lucide-react';\n\ninterface SubCategoryItemProps {\n  subCategory: SubCategory;\n  onEdit: () => void;\n  onDelete: () => void;\n  balancesVisible: boolean;\n}\n\nexport default function SubCategoryItem({ subCategory, onEdit, onDelete, balancesVisible }: SubCategoryItemProps) {\n  return (\n    <div className=\"flex items-center justify-between p-2 border-b border-border/50 last:border-b-0\">\n      <div className=\"flex-1\">\n        <p className=\"text-sm font-medium\">{subCategory.name}</p>\n        <p className=\"text-xs text-muted-foreground\">\n          Allocated: {balancesVisible ? `R ${subCategory.allocatedAmount.toFixed(2)}` : 'R ••••'}\n        </p>\n      </div>\n      <div className=\"flex gap-1\">\n        <Button variant=\"ghost\" size=\"icon\" onClick={onEdit} className=\"h-7 w-7\">\n          <Edit3 className=\"h-4 w-4\" />\n          <span className=\"sr-only\">Edit Subcategory</span>\n        </Button>\n        <Button variant=\"ghost\" size=\"icon\" onClick={onDelete} className=\"h-7 w-7 text-destructive hover:text-destructive\">\n          <Trash2 className=\"h-4 w-4\" />\n          <span className=\"sr-only\">Delete Subcategory</span>\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAAA;AAJA;;;;AAae,SAAS,gBAAgB,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAwB;IAC9G,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAuB,YAAY,IAAI;;;;;;kCACpD,6LAAC;wBAAE,WAAU;;4BAAgC;4BAC/B,kBAAkB,CAAC,EAAE,EAAE,YAAY,eAAe,CAAC,OAAO,CAAC,IAAI,GAAG;;;;;;;;;;;;;0BAGlF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAO,SAAS;wBAAQ,WAAU;;0CAC7D,6LAAC,6MAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAE5B,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAO,SAAS;wBAAU,WAAU;;0CAC/D,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;KArBwB", "debugId": null}}, {"offset": {"line": 3211, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3250, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState, formState } = useFormContext()\n\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nconst FormItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\n    </FormItemContext.Provider>\n  )\n})\nFormItem.displayName = \"FormItem\"\n\nconst FormLabel = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      ref={ref}\n      className={cn(error && \"text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n})\nFormLabel.displayName = \"FormLabel\"\n\nconst FormControl = React.forwardRef<\n  React.ElementRef<typeof Slot>,\n  React.ComponentPropsWithoutRef<typeof Slot>\n>(({ ...props }, ref) => {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      ref={ref}\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n})\nFormControl.displayName = \"FormControl\"\n\nconst FormDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      ref={ref}\n      id={formDescriptionId}\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      {...props}\n    />\n  )\n})\nFormDescription.displayName = \"FormDescription\"\n\nconst FormMessage = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      ref={ref}\n      id={formMessageId}\n      className={cn(\"text-sm font-medium text-destructive\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n})\nFormMessage.displayName = \"FormMessage\"\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AASA;AACA;;;AAfA;;;;;;AAiBA,MAAM,OAAO,iKAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6LAAC,iKAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;GArBM;;QAGiC,iKAAA,CAAA,iBAAc;;;AAwBrD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,MAAM,yBAAW,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;;IAC1B,MAAM,KAAK,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAErB,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6LAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAa,GAAG,KAAK;;;;;;;;;;;AAGrE;;AACA,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;;IAC1B,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,oBAAoB;QAC3C,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;;QAVgC;;;;QAAA;;;;AAWhC,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAGjC,CAAC,EAAE,GAAG,OAAO,EAAE;;IACf,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6LAAC,mKAAA,CAAA,OAAI;QACH,KAAK;QACL,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;;QAfkE;;;;QAAA;;;;AAgBlE,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;;IAC1B,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6LAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;;QAVgC;;;;QAAA;;;;AAWhC,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;;IACpC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM;IAEpD,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;kBAER;;;;;;AAGP;;QAjBmC;;;;QAAA;;;;AAkBnC,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3468, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/budget/SubCategoryForm.tsx"], "sourcesContent": ["\n'use client';\n\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { useForm } from 'react-hook-form';\nimport * as z from 'zod';\nimport { Button } from '@/components/ui/button';\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '@/components/ui/form';\nimport { Input } from '@/components/ui/input';\nimport { SubCategory } from '@/lib/types';\n\nconst subCategoryFormSchema = z.object({\n  name: z.string().min(1, { message: 'Subcategory name is required.' }).max(50, { message: 'Name must be 50 characters or less.' }),\n  allocatedAmount: z.preprocess(\n    (val) => (typeof val === 'string' ? parseFloat(val) : val),\n    z.number().min(0, { message: 'Allocated amount must be a positive number.' })\n  ),\n});\n\ntype SubCategoryFormValues = z.infer<typeof subCategoryFormSchema>;\n\ninterface SubCategoryFormProps {\n  onSubmit: (values: SubCategoryFormValues) => boolean;\n  initialData?: Partial<SubCategory>;\n  onClose: () => void;\n  parentCategoryName: string;\n  balancesVisible: boolean; // To control input type and display\n}\n\nexport default function SubCategoryForm({ onSubmit, initialData, onClose, parentCategoryName, balancesVisible }: SubCategoryFormProps) {\n  const form = useForm<SubCategoryFormValues>({\n    resolver: zodResolver(subCategoryFormSchema),\n    defaultValues: {\n      name: initialData?.name || '',\n      allocatedAmount: initialData?.allocatedAmount || 0,\n    },\n  });\n\n  const handleSubmit = (values: SubCategoryFormValues) => {\n    const success = onSubmit(values);\n    if (success) {\n      form.reset();\n      onClose();\n    }\n  };\n\n  return (\n    <Form {...form}>\n      <form onSubmit={form.handleSubmit(handleSubmit)} className=\"space-y-4\">\n        <p className=\"text-sm text-muted-foreground\">Adding to: <span className=\"font-semibold\">{parentCategoryName}</span></p>\n        <FormField\n          control={form.control}\n          name=\"name\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel>Subcategory Name</FormLabel>\n              <FormControl>\n                <Input placeholder=\"e.g., Fruits & Vegetables\" {...field} />\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n        <FormField\n          control={form.control}\n          name=\"allocatedAmount\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel>Allocated Amount (R)</FormLabel>\n              <FormControl>\n                 <Input \n                  type={balancesVisible ? \"number\" : \"password\"} \n                  placeholder={balancesVisible ? \"e.g., 100\" : \"••••\"}\n                  {...field} \n                  value={balancesVisible ? field.value : (field.value > 0 ? \"••••\" : \"0\")} // Show \"••••\" if hidden and value > 0\n                  onChange={(e) => {\n                    if (balancesVisible) {\n                      field.onChange(e.target.value === \"\" ? 0 : parseFloat(e.target.value));\n                    } else {\n                      // If not visible, we still need to update the underlying value.\n                      // This assumes the user knows what they are typing or is unhiding to see.\n                      // A more complex solution would be needed for true masked input that updates a numeric value.\n                      // For now, allow numeric input to update state, but display is masked.\n                       const numericValue = parseFloat(e.target.value);\n                       if (!isNaN(numericValue)) {\n                         field.onChange(numericValue);\n                       } else if (e.target.value === \"\") {\n                         field.onChange(0);\n                       }\n                    }\n                  }}\n                  step=\"any\" \n                />\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n        <div className=\"flex justify-end gap-2 pt-2\">\n          <Button type=\"button\" variant=\"outline\" onClick={onClose}>Cancel</Button>\n          <Button type=\"submit\">\n            {initialData?.id ? 'Save Changes' : 'Add Subcategory'}\n          </Button>\n        </div>\n      </form>\n    </Form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AACA;AACA;AAQA;;;AAdA;;;;;;;AAiBA,MAAM,wBAAwB,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,EAAE;IACrC,MAAM,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAAgC,GAAG,GAAG,CAAC,IAAI;QAAE,SAAS;IAAsC;IAC/H,iBAAiB,CAAA,GAAA,oJAAA,CAAA,aAAY,AAAD,EAC1B,CAAC,MAAS,OAAO,QAAQ,WAAW,WAAW,OAAO,KACtD,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAA8C;AAE/E;AAYe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,kBAAkB,EAAE,eAAe,EAAwB;;IACnI,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAyB;QAC1C,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM,aAAa,QAAQ;YAC3B,iBAAiB,aAAa,mBAAmB;QACnD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,UAAU,SAAS;QACzB,IAAI,SAAS;YACX,KAAK,KAAK;YACV;QACF;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YAAK,UAAU,KAAK,YAAY,CAAC;YAAe,WAAU;;8BACzD,6LAAC;oBAAE,WAAU;;wBAAgC;sCAAW,6LAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;;8BACzF,6LAAC,mIAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;8CACP,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;wCAAC,aAAY;wCAA6B,GAAG,KAAK;;;;;;;;;;;8CAE1D,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAIlB,6LAAC,mIAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;8CACP,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,mIAAA,CAAA,cAAW;8CACT,cAAA,6LAAC,oIAAA,CAAA,QAAK;wCACL,MAAM,kBAAkB,WAAW;wCACnC,aAAa,kBAAkB,cAAc;wCAC5C,GAAG,KAAK;wCACT,OAAO,kBAAkB,MAAM,KAAK,GAAI,MAAM,KAAK,GAAG,IAAI,SAAS;wCACnE,UAAU,CAAC;4CACT,IAAI,iBAAiB;gDACnB,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,IAAI,WAAW,EAAE,MAAM,CAAC,KAAK;4CACtE,OAAO;gDACL,gEAAgE;gDAChE,0EAA0E;gDAC1E,8FAA8F;gDAC9F,uEAAuE;gDACtE,MAAM,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC9C,IAAI,CAAC,MAAM,eAAe;oDACxB,MAAM,QAAQ,CAAC;gDACjB,OAAO,IAAI,EAAE,MAAM,CAAC,KAAK,KAAK,IAAI;oDAChC,MAAM,QAAQ,CAAC;gDACjB;4CACH;wCACF;wCACA,MAAK;;;;;;;;;;;8CAGT,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAIlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,SAAQ;4BAAU,SAAS;sCAAS;;;;;;sCAC1D,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;sCACV,aAAa,KAAK,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;AAMhD;GA9EwB;;QACT,iKAAA,CAAA,UAAO;;;KADE", "debugId": null}}, {"offset": {"line": 3697, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3743, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/budget/CategoryItem.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useState } from 'react';\nimport type { Category, SubCategory } from '@/lib/types';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport SubCategoryItem from './SubCategoryItem';\nimport SubCategoryForm from './SubCategoryForm';\nimport { PlusCircle, AlertTriangle, ListCollapse, ListTree } from 'lucide-react';\nimport { Progress } from '@/components/ui/progress';\nimport { useToast } from '@/hooks/use-toast';\n\ninterface CategoryItemProps {\n  category: Category;\n  onUpdateCategory: (id: string, name: string, budget: number) => void;\n  onDeleteCategory: (id: string) => void;\n  onAddSubCategory: (parentId: string, name: string, allocatedAmount: number) => boolean;\n  onUpdateSubCategory: (parentId: string, subId: string, name: string, newAllocatedAmount: number) => boolean;\n  onDeleteSubCategory: (parentId: string, subId: string) => void;\n  balancesVisible: boolean; // Global balance visibility\n  categoryIsVisible: boolean; // Individual category visibility\n  onToggleVisibility: () => void;\n}\n\nexport default function CategoryItem({\n  category,\n  // onUpdateCategory, // Handled by CategoryManager overlay buttons\n  // onDeleteCategory, // Handled by CategoryManager overlay buttons\n  onAddSubCategory,\n  onUpdateSubCategory,\n  onDeleteSubCategory,\n  balancesVisible, // Global visibility\n  categoryIsVisible, // This specific category's visibility\n  // onToggleVisibility // Handled by CategoryManager overlay buttons\n}: CategoryItemProps) {\n  const [isSubCategoryFormOpen, setIsSubCategoryFormOpen] = useState(false);\n  const [editingSubCategory, setEditingSubCategory] = useState<SubCategory | null>(null);\n  const { toast } = useToast();\n\n  const displayCategoryDetails = balancesVisible && categoryIsVisible;\n\n  const totalSubCategoryAllocation = category.subCategories.reduce((sum, sc) => sum + sc.allocatedAmount, 0);\n  const remainingInCategoryBudget = category.budget - totalSubCategoryAllocation;\n  const isOverAllocatedWithinCategory = remainingInCategoryBudget < 0;\n  const allocationPercentage = category.budget > 0 ? (totalSubCategoryAllocation / category.budget) * 100 : 0;\n\n  const handleAddSubCategory = (values: { name: string; allocatedAmount: number }) => {\n    const success = onAddSubCategory(category.id, values.name, values.allocatedAmount);\n    if(success) {\n      toast({ title: \"Subcategory Added\", description: `${values.name} added to ${category.name}.` });\n      setIsSubCategoryFormOpen(false);\n    } else {\n      toast({ title: \"Error\", description: `Cannot allocate ${displayCategoryDetails ? 'R'+values.allocatedAmount.toFixed(2) : 'amount'}. Exceeds remaining budget in ${category.name}.`, variant: \"destructive\" });\n    }\n    return success;\n  };\n\n  const handleUpdateSubCategory = (values: { name: string; allocatedAmount: number }) => {\n    if (!editingSubCategory) return false;\n    const success = onUpdateSubCategory(category.id, editingSubCategory.id, values.name, values.allocatedAmount);\n     if(success) {\n      toast({ title: \"Subcategory Updated\", description: `${values.name} updated.` });\n      setEditingSubCategory(null);\n      setIsSubCategoryFormOpen(false);\n    } else {\n      toast({ title: \"Error\", description: `Cannot update allocation. Exceeds remaining budget in ${category.name}.`, variant: \"destructive\" });\n    }\n    return success;\n  };\n\n  const formatCurrency = (amount: number) => {\n    if (!displayCategoryDetails) return 'R ••••';\n    return `R ${amount.toFixed(2)}`;\n  };\n\n  return (\n    <Card className=\"mb-3\">\n      <CardHeader className=\"flex flex-row items-start justify-between pb-2 pt-3 px-4\">\n        <div>\n          <CardTitle className=\"text-base font-headline\">{category.name}</CardTitle>\n          <CardDescription className=\"text-xs\">Budget: {formatCurrency(category.budget)}</CardDescription>\n        </div>\n      </CardHeader>\n      <CardContent className=\"px-4 pb-3 space-y-2\">\n        <div className=\"text-xs space-y-1\">\n            <div className=\"flex justify-between\">\n                <span>Subcategories Total:</span>\n                <span>{formatCurrency(totalSubCategoryAllocation)}</span>\n            </div>\n            <Progress value={Math.min(allocationPercentage, 100)} className={isOverAllocatedWithinCategory ? \"bg-destructive/70 [&>*]:bg-destructive\" : \"[&>*]:bg-primary\"} />\n            <div className={`flex justify-between font-medium ${isOverAllocatedWithinCategory ? 'text-destructive' : 'text-green-600'}`}>\n                <span>Remaining in Budget:</span>\n                <span>{formatCurrency(remainingInCategoryBudget)}</span>\n            </div>\n        </div>\n\n        {isOverAllocatedWithinCategory && displayCategoryDetails && (\n          <div className=\"flex items-center gap-1 text-destructive text-xs p-1.5 bg-destructive/10 rounded-md\">\n            <AlertTriangle className=\"h-3 w-3\" />\n            <span>Subcategory allocations exceed this category's budget!</span>\n          </div>\n        )}\n\n        {category.subCategories.length > 0 && (\n          <Accordion type=\"single\" collapsible className=\"w-full text-sm\">\n            <AccordionItem value={`cat-${category.id}-subcategories`} className=\"border-t pt-2\">\n              <AccordionTrigger className=\"py-1 text-xs hover:no-underline justify-start gap-1 group\">\n                <ListCollapse className=\"h-3 w-3 hidden group-data-[state=open]:block\" />\n                <ListTree className=\"h-3 w-3 block group-data-[state=open]:hidden\" />\n                <span>Subcategories ({category.subCategories.length})</span>\n              </AccordionTrigger>\n              <AccordionContent className=\"pt-1 pb-0 pl-2 border-l ml-1.5\">\n                {category.subCategories.map((sc) => (\n                  <SubCategoryItem\n                    key={sc.id}\n                    subCategory={sc}\n                    onEdit={() => {\n                      setEditingSubCategory(sc);\n                      setIsSubCategoryFormOpen(true);\n                    }}\n                    onDelete={() => onDeleteSubCategory(category.id, sc.id)}\n                    balancesVisible={displayCategoryDetails} // Pass effective visibility\n                  />\n                ))}\n              </AccordionContent>\n            </AccordionItem>\n          </Accordion>\n        )}\n      </CardContent>\n      <CardFooter className=\"px-4 pb-3 pt-0\">\n        <Dialog open={isSubCategoryFormOpen} onOpenChange={(isOpen) => {\n          setIsSubCategoryFormOpen(isOpen);\n          if (!isOpen) setEditingSubCategory(null);\n        }}>\n          <DialogTrigger asChild>\n            <Button variant=\"outline\" size=\"sm\" className=\"w-full text-xs\" onClick={() => { setEditingSubCategory(null); setIsSubCategoryFormOpen(true); }}>\n              <PlusCircle className=\"mr-1 h-3 w-3\" /> Add Subcategory\n            </Button>\n          </DialogTrigger>\n          <DialogContent className=\"sm:max-w-[425px]\">\n            <DialogHeader>\n              <DialogTitle className=\"font-headline\">{editingSubCategory ? 'Edit' : 'Add'} Subcategory</DialogTitle>\n            </DialogHeader>\n            <SubCategoryForm\n              onSubmit={editingSubCategory ? handleUpdateSubCategory : handleAddSubCategory}\n              initialData={editingSubCategory || {}}\n              onClose={() => { setIsSubCategoryFormOpen(false); setEditingSubCategory(null); }}\n              parentCategoryName={category.name}\n              balancesVisible={displayCategoryDetails}\n            />\n          </DialogContent>\n        </Dialog>\n      </CardFooter>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AAZA;;;;;;;;;;;AA0Be,SAAS,aAAa,EACnC,QAAQ,EACR,kEAAkE;AAClE,kEAAkE;AAClE,gBAAgB,EAChB,mBAAmB,EACnB,mBAAmB,EACnB,eAAe,EACf,iBAAiB,EAEC;;IAClB,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACjF,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,yBAAyB,mBAAmB;IAElD,MAAM,6BAA6B,SAAS,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,KAAO,MAAM,GAAG,eAAe,EAAE;IACxG,MAAM,4BAA4B,SAAS,MAAM,GAAG;IACpD,MAAM,gCAAgC,4BAA4B;IAClE,MAAM,uBAAuB,SAAS,MAAM,GAAG,IAAI,AAAC,6BAA6B,SAAS,MAAM,GAAI,MAAM;IAE1G,MAAM,uBAAuB,CAAC;QAC5B,MAAM,UAAU,iBAAiB,SAAS,EAAE,EAAE,OAAO,IAAI,EAAE,OAAO,eAAe;QACjF,IAAG,SAAS;YACV,MAAM;gBAAE,OAAO;gBAAqB,aAAa,GAAG,OAAO,IAAI,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC;YAAC;YAC7F,yBAAyB;QAC3B,OAAO;YACL,MAAM;gBAAE,OAAO;gBAAS,aAAa,CAAC,gBAAgB,EAAE,yBAAyB,MAAI,OAAO,eAAe,CAAC,OAAO,CAAC,KAAK,SAAS,8BAA8B,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC;gBAAE,SAAS;YAAc;QAC7M;QACA,OAAO;IACT;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,oBAAoB,OAAO;QAChC,MAAM,UAAU,oBAAoB,SAAS,EAAE,EAAE,mBAAmB,EAAE,EAAE,OAAO,IAAI,EAAE,OAAO,eAAe;QAC1G,IAAG,SAAS;YACX,MAAM;gBAAE,OAAO;gBAAuB,aAAa,GAAG,OAAO,IAAI,CAAC,SAAS,CAAC;YAAC;YAC7E,sBAAsB;YACtB,yBAAyB;QAC3B,OAAO;YACL,MAAM;gBAAE,OAAO;gBAAS,aAAa,CAAC,sDAAsD,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC;gBAAE,SAAS;YAAc;QACzI;QACA,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,wBAAwB,OAAO;QACpC,OAAO,CAAC,EAAE,EAAE,OAAO,OAAO,CAAC,IAAI;IACjC;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;;sCACC,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAA2B,SAAS,IAAI;;;;;;sCAC7D,6LAAC,mIAAA,CAAA,kBAAe;4BAAC,WAAU;;gCAAU;gCAAS,eAAe,SAAS,MAAM;;;;;;;;;;;;;;;;;;0BAGhF,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;kDAAK;;;;;;kDACN,6LAAC;kDAAM,eAAe;;;;;;;;;;;;0CAE1B,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,OAAO,KAAK,GAAG,CAAC,sBAAsB;gCAAM,WAAW,gCAAgC,2CAA2C;;;;;;0CAC5I,6LAAC;gCAAI,WAAW,CAAC,iCAAiC,EAAE,gCAAgC,qBAAqB,kBAAkB;;kDACvH,6LAAC;kDAAK;;;;;;kDACN,6LAAC;kDAAM,eAAe;;;;;;;;;;;;;;;;;;oBAI7B,iCAAiC,wCAChC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC;0CAAK;;;;;;;;;;;;oBAIT,SAAS,aAAa,CAAC,MAAM,GAAG,mBAC/B,6LAAC,wIAAA,CAAA,YAAS;wBAAC,MAAK;wBAAS,WAAW;wBAAC,WAAU;kCAC7C,cAAA,6LAAC,wIAAA,CAAA,gBAAa;4BAAC,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,cAAc,CAAC;4BAAE,WAAU;;8CAClE,6LAAC,wIAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;;gDAAK;gDAAgB,SAAS,aAAa,CAAC,MAAM;gDAAC;;;;;;;;;;;;;8CAEtD,6LAAC,wIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CACzB,SAAS,aAAa,CAAC,GAAG,CAAC,CAAC,mBAC3B,6LAAC,kJAAA,CAAA,UAAe;4CAEd,aAAa;4CACb,QAAQ;gDACN,sBAAsB;gDACtB,yBAAyB;4CAC3B;4CACA,UAAU,IAAM,oBAAoB,SAAS,EAAE,EAAE,GAAG,EAAE;4CACtD,iBAAiB;2CAPZ,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAexB,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,MAAM;oBAAuB,cAAc,CAAC;wBAClD,yBAAyB;wBACzB,IAAI,CAAC,QAAQ,sBAAsB;oBACrC;;sCACE,6LAAC,qIAAA,CAAA,gBAAa;4BAAC,OAAO;sCACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;gCAAiB,SAAS;oCAAQ,sBAAsB;oCAAO,yBAAyB;gCAAO;;kDAC3I,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAG3C,6LAAC,qIAAA,CAAA,gBAAa;4BAAC,WAAU;;8CACvB,6LAAC,qIAAA,CAAA,eAAY;8CACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;wCAAC,WAAU;;4CAAiB,qBAAqB,SAAS;4CAAM;;;;;;;;;;;;8CAE9E,6LAAC,kJAAA,CAAA,UAAe;oCACd,UAAU,qBAAqB,0BAA0B;oCACzD,aAAa,sBAAsB,CAAC;oCACpC,SAAS;wCAAQ,yBAAyB;wCAAQ,sBAAsB;oCAAO;oCAC/E,oBAAoB,SAAS,IAAI;oCACjC,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B;GAnIwB;;QAaJ,+HAAA,CAAA,WAAQ;;;KAbJ", "debugId": null}}, {"offset": {"line": 4144, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/budget/CategoryForm.tsx"], "sourcesContent": ["'use client';\n\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { useForm } from 'react-hook-form';\nimport * as z from 'zod';\nimport { Button } from '@/components/ui/button';\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '@/components/ui/form';\nimport { Input } from '@/components/ui/input';\nimport { Category } from '@/lib/types';\n\nconst categoryFormSchema = z.object({\n  name: z.string().min(1, { message: 'Category name is required.' }).max(50, { message: 'Name must be 50 characters or less.' }),\n  budget: z.preprocess(\n    (val) => (typeof val === 'string' ? parseFloat(val) : val),\n    z.number().min(0, { message: 'Budget must be a positive number.' })\n  ),\n});\n\ntype CategoryFormValues = z.infer<typeof categoryFormSchema>;\n\ninterface CategoryFormProps {\n  onSubmit: (values: CategoryFormValues) => void;\n  initialData?: Partial<Category>;\n  onClose: () => void;\n}\n\nexport default function CategoryForm({ onSubmit, initialData, onClose }: CategoryFormProps) {\n  const form = useForm<CategoryFormValues>({\n    resolver: zodResolver(categoryFormSchema),\n    defaultValues: {\n      name: initialData?.name || '',\n      budget: initialData?.budget || 0,\n    },\n  });\n\n  const handleSubmit = (values: CategoryFormValues) => {\n    onSubmit(values);\n    form.reset();\n    onClose();\n  };\n\n  return (\n    <Form {...form}>\n      <form onSubmit={form.handleSubmit(handleSubmit)} className=\"space-y-4\">\n        <FormField\n          control={form.control}\n          name=\"name\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel>Category Name</FormLabel>\n              <FormControl>\n                <Input placeholder=\"e.g., Groceries\" {...field} />\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n        <FormField\n          control={form.control}\n          name=\"budget\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel>Budget Amount (R)</FormLabel>\n              <FormControl>\n                <Input type=\"number\" placeholder=\"e.g., 500\" {...field} step=\"any\" />\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n        <div className=\"flex justify-end gap-2 pt-2\">\n          <Button type=\"button\" variant=\"outline\" onClick={onClose}>Cancel</Button>\n          <Button type=\"submit\">\n            {initialData?.id ? 'Save Changes' : 'Add Category'}\n          </Button>\n        </div>\n      </form>\n    </Form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AAQA;;;AAdA;;;;;;;AAiBA,MAAM,qBAAqB,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,EAAE;IAClC,MAAM,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B,GAAG,GAAG,CAAC,IAAI;QAAE,SAAS;IAAsC;IAC5H,QAAQ,CAAA,GAAA,oJAAA,CAAA,aAAY,AAAD,EACjB,CAAC,MAAS,OAAO,QAAQ,WAAW,WAAW,OAAO,KACtD,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoC;AAErE;AAUe,SAAS,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAqB;;IACxF,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAsB;QACvC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM,aAAa,QAAQ;YAC3B,QAAQ,aAAa,UAAU;QACjC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,SAAS;QACT,KAAK,KAAK;QACV;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YAAK,UAAU,KAAK,YAAY,CAAC;YAAe,WAAU;;8BACzD,6LAAC,mIAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;8CACP,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;wCAAC,aAAY;wCAAmB,GAAG,KAAK;;;;;;;;;;;8CAEhD,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAIlB,6LAAC,mIAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;8CACP,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;wCAAC,MAAK;wCAAS,aAAY;wCAAa,GAAG,KAAK;wCAAE,MAAK;;;;;;;;;;;8CAE/D,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAIlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,SAAQ;4BAAU,SAAS;sCAAS;;;;;;sCAC1D,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;sCACV,aAAa,KAAK,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;AAMhD;GArDwB;;QACT,iKAAA,CAAA,UAAO;;;KADE", "debugId": null}}, {"offset": {"line": 4336, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,8KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,8KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,8KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,8KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,8KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4488, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/budget/CategoryManager.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useState } from 'react';\nimport { Category } from '@/lib/types';\nimport CategoryItem from './CategoryItem';\nimport CategoryForm from './CategoryForm';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';\nimport { PlusCircle, Edit3, Trash2, LayoutList, Eye, EyeOff } from 'lucide-react';\nimport { useToast } from '@/hooks/use-toast';\n\ninterface CategoryManagerProps {\n  categories: Category[];\n  onAddCategory: (name: string, budget: number) => void;\n  onUpdateCategory: (id: string, newName: string, newBudget: number) => void;\n  onDeleteCategory: (id: string) => void;\n  onAddSubCategory: (parentId: string, name: string, allocatedAmount: number) => boolean;\n  onUpdateSubCategory: (parentId: string, subId: string, name: string, newAllocatedAmount: number) => boolean;\n  onDeleteSubCategory: (parentId: string, subId: string) => void;\n  onToggleCategoryVisibility: (categoryId: string) => void;\n  balancesVisible: boolean; // Global balance visibility\n}\n\nexport default function CategoryManager({\n  categories,\n  onAddCategory,\n  onUpdateCategory,\n  onDeleteCategory,\n  onAddSubCategory,\n  onUpdateSubCategory,\n  onDeleteSubCategory,\n  onToggleCategoryVisibility,\n  balancesVisible,\n}: CategoryManagerProps) {\n  const [isCategoryFormOpen, setIsCategoryFormOpen] = useState(false);\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null);\n  const [deletingCategoryId, setDeletingCategoryId] = useState<string | null>(null);\n  const { toast } = useToast();\n\n  const handleAddCategory = (values: { name: string; budget: number }) => {\n    onAddCategory(values.name, values.budget);\n    toast({ title: \"Category Added\", description: `${values.name} has been added.`});\n    setIsCategoryFormOpen(false);\n  };\n\n  const handleUpdateCategory = (values: { name: string; budget: number }) => {\n    if (!editingCategory) return;\n    onUpdateCategory(editingCategory.id, values.name, values.budget);\n    toast({ title: \"Category Updated\", description: `${values.name} has been updated.`});\n    setEditingCategory(null);\n    setIsCategoryFormOpen(false);\n  };\n\n  const confirmDeleteCategory = () => {\n    if (deletingCategoryId) {\n      const categoryToDelete = categories.find(c => c.id === deletingCategoryId);\n      onDeleteCategory(deletingCategoryId);\n      toast({ title: \"Category Deleted\", description: `${categoryToDelete?.name || 'Category'} has been deleted.`});\n      setDeletingCategoryId(null);\n    }\n  };\n\n  return (\n    <Card>\n      <CardHeader className=\"flex flex-row items-center justify-between pb-2\">\n        <CardTitle className=\"text-lg flex items-center gap-2 font-headline\">\n          <LayoutList className=\"h-5 w-5 text-primary\" />\n          Budget Categories\n        </CardTitle>\n        <Dialog open={isCategoryFormOpen} onOpenChange={(isOpen) => {\n            setIsCategoryFormOpen(isOpen);\n            if (!isOpen) setEditingCategory(null);\n        }}>\n          <DialogTrigger asChild>\n            <Button size=\"sm\" onClick={() => { setEditingCategory(null); setIsCategoryFormOpen(true); }}>\n              <PlusCircle className=\"mr-2 h-4 w-4\" /> Add Category\n            </Button>\n          </DialogTrigger>\n          <DialogContent className=\"sm:max-w-[425px]\">\n            <DialogHeader>\n              <DialogTitle className=\"font-headline\">{editingCategory ? 'Edit' : 'Add'} Category</DialogTitle>\n            </DialogHeader>\n            <CategoryForm\n              onSubmit={editingCategory ? handleUpdateCategory : handleAddCategory}\n              initialData={editingCategory || {}}\n              onClose={() => { setIsCategoryFormOpen(false); setEditingCategory(null); }}\n            />\n          </DialogContent>\n        </Dialog>\n      </CardHeader>\n      <CardContent className=\"pt-2\">\n        {categories.length === 0 ? (\n          <p className=\"text-sm text-muted-foreground text-center py-4\">No categories added yet. Click \"Add Category\" to start.</p>\n        ) : (\n          <div className=\"space-y-2\">\n            {categories.map((category) => (\n              <div key={category.id} className=\"relative group\">\n                 <CategoryItem\n                    category={category}\n                    onUpdateCategory={onUpdateCategory}\n                    onDeleteCategory={onDeleteCategory}\n                    onAddSubCategory={onAddSubCategory}\n                    onUpdateSubCategory={onUpdateSubCategory}\n                    onDeleteSubCategory={onDeleteSubCategory}\n                    balancesVisible={balancesVisible} // Pass global visibility\n                    categoryIsVisible={category.isVisible ?? true} // Pass individual category visibility\n                    onToggleVisibility={() => onToggleCategoryVisibility(category.id)}\n                  />\n                  <div className=\"absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n                    <Button variant=\"ghost\" size=\"icon\" className=\"h-7 w-7 bg-card hover:bg-muted\" onClick={() => onToggleCategoryVisibility(category.id)}>\n                      {(category.isVisible ?? true) ? <Eye className=\"h-4 w-4\" /> : <EyeOff className=\"h-4 w-4\" />}\n                       <span className=\"sr-only\">{(category.isVisible ?? true) ? 'Hide Category Balances' : 'Show Category Balances'}</span>\n                    </Button>\n                    <Button variant=\"ghost\" size=\"icon\" className=\"h-7 w-7 bg-card hover:bg-muted\" onClick={() => { setEditingCategory(category); setIsCategoryFormOpen(true); }}>\n                      <Edit3 className=\"h-4 w-4\" />\n                       <span className=\"sr-only\">Edit Category</span>\n                    </Button>\n                    <Button variant=\"ghost\" size=\"icon\" className=\"h-7 w-7 text-destructive hover:text-destructive bg-card hover:bg-muted\" onClick={() => setDeletingCategoryId(category.id)}>\n                      <Trash2 className=\"h-4 w-4\" />\n                       <span className=\"sr-only\">Delete Category</span>\n                    </Button>\n                  </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </CardContent>\n      <AlertDialog open={!!deletingCategoryId} onOpenChange={() => setDeletingCategoryId(null)}>\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle>Are you sure?</AlertDialogTitle>\n            <AlertDialogDescription>\n              This action will delete the category\n              {categories.find(c => c.id === deletingCategoryId)?.subCategories.length ?? 0 > 0 ? \" and all its subcategories\" : \"\"}.\n              This cannot be undone.\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <AlertDialogFooter>\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\n            <AlertDialogAction onClick={confirmDeleteCategory} className=\"bg-destructive hover:bg-destructive/90\">\n              Delete\n            </AlertDialogAction>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAXA;;;;;;;;;;AAyBe,SAAS,gBAAgB,EACtC,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,mBAAmB,EACnB,mBAAmB,EACnB,0BAA0B,EAC1B,eAAe,EACM;;IACrB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,oBAAoB,CAAC;QACzB,cAAc,OAAO,IAAI,EAAE,OAAO,MAAM;QACxC,MAAM;YAAE,OAAO;YAAkB,aAAa,GAAG,OAAO,IAAI,CAAC,gBAAgB,CAAC;QAAA;QAC9E,sBAAsB;IACxB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,CAAC,iBAAiB;QACtB,iBAAiB,gBAAgB,EAAE,EAAE,OAAO,IAAI,EAAE,OAAO,MAAM;QAC/D,MAAM;YAAE,OAAO;YAAoB,aAAa,GAAG,OAAO,IAAI,CAAC,kBAAkB,CAAC;QAAA;QAClF,mBAAmB;QACnB,sBAAsB;IACxB;IAEA,MAAM,wBAAwB;QAC5B,IAAI,oBAAoB;YACtB,MAAM,mBAAmB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACvD,iBAAiB;YACjB,MAAM;gBAAE,OAAO;gBAAoB,aAAa,GAAG,kBAAkB,QAAQ,WAAW,kBAAkB,CAAC;YAAA;YAC3G,sBAAsB;QACxB;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAyB;;;;;;;kCAGjD,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAoB,cAAc,CAAC;4BAC7C,sBAAsB;4BACtB,IAAI,CAAC,QAAQ,mBAAmB;wBACpC;;0CACE,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS;wCAAQ,mBAAmB;wCAAO,sBAAsB;oCAAO;;sDACxF,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAG3C,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,6LAAC,qIAAA,CAAA,eAAY;kDACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,WAAU;;gDAAiB,kBAAkB,SAAS;gDAAM;;;;;;;;;;;;kDAE3E,6LAAC,+IAAA,CAAA,UAAY;wCACX,UAAU,kBAAkB,uBAAuB;wCACnD,aAAa,mBAAmB,CAAC;wCACjC,SAAS;4CAAQ,sBAAsB;4CAAQ,mBAAmB;wCAAO;;;;;;;;;;;;;;;;;;;;;;;;0BAKjF,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACpB,WAAW,MAAM,KAAK,kBACrB,6LAAC;oBAAE,WAAU;8BAAiD;;;;;yCAE9D,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4BAAsB,WAAU;;8CAC9B,6LAAC,+IAAA,CAAA,UAAY;oCACV,UAAU;oCACV,kBAAkB;oCAClB,kBAAkB;oCAClB,kBAAkB;oCAClB,qBAAqB;oCACrB,qBAAqB;oCACrB,iBAAiB;oCACjB,mBAAmB,SAAS,SAAS,IAAI;oCACzC,oBAAoB,IAAM,2BAA2B,SAAS,EAAE;;;;;;8CAElE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAO,WAAU;4CAAiC,SAAS,IAAM,2BAA2B,SAAS,EAAE;;gDAChI,SAAS,SAAS,IAAI,qBAAQ,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;yEAAe,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAC/E,6LAAC;oDAAK,WAAU;8DAAW,AAAC,SAAS,SAAS,IAAI,OAAQ,2BAA2B;;;;;;;;;;;;sDAExF,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAO,WAAU;4CAAiC,SAAS;gDAAQ,mBAAmB;gDAAW,sBAAsB;4CAAO;;8DACzJ,6LAAC,6MAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE7B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAO,WAAU;4CAAyE,SAAS,IAAM,sBAAsB,SAAS,EAAE;;8DACrK,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;2BAvBzB,SAAS,EAAE;;;;;;;;;;;;;;;0BA+B7B,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM,CAAC,CAAC;gBAAoB,cAAc,IAAM,sBAAsB;0BACjF,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;;wCAAC;wCAErB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,qBAAqB,cAAc,UAAU,IAAI,IAAI,+BAA+B;wCAAG;;;;;;;;;;;;;sCAI1H,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,6LAAC,8IAAA,CAAA,oBAAiB;oCAAC,SAAS;oCAAuB,WAAU;8CAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlH;GA5HwB;;QAcJ,+HAAA,CAAA,WAAQ;;;KAdJ", "debugId": null}}, {"offset": {"line": 4894, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/chart.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as RechartsPrimitive from \"recharts\"\n\nimport { cn } from \"@/lib/utils\"\n\n// Format: { THEME_NAME: CSS_SELECTOR }\nconst THEMES = { light: \"\", dark: \".dark\" } as const\n\nexport type ChartConfig = {\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & (\n    | { color?: string; theme?: never }\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\n  )\n}\n\ntype ChartContextProps = {\n  config: ChartConfig\n}\n\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\n\nfunction useChart() {\n  const context = React.useContext(ChartContext)\n\n  if (!context) {\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\n  }\n\n  return context\n}\n\nconst ChartContainer = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    config: ChartConfig\n    children: React.ComponentProps<\n      typeof RechartsPrimitive.ResponsiveContainer\n    >[\"children\"]\n  }\n>(({ id, className, children, config, ...props }, ref) => {\n  const uniqueId = React.useId()\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\n\n  return (\n    <ChartContext.Provider value={{ config }}>\n      <div\n        data-chart={chartId}\n        ref={ref}\n        className={cn(\n          \"flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none\",\n          className\n        )}\n        {...props}\n      >\n        <ChartStyle id={chartId} config={config} />\n        <RechartsPrimitive.ResponsiveContainer>\n          {children}\n        </RechartsPrimitive.ResponsiveContainer>\n      </div>\n    </ChartContext.Provider>\n  )\n})\nChartContainer.displayName = \"Chart\"\n\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\n  const colorConfig = Object.entries(config).filter(\n    ([, config]) => config.theme || config.color\n  )\n\n  if (!colorConfig.length) {\n    return null\n  }\n\n  return (\n    <style\n      dangerouslySetInnerHTML={{\n        __html: Object.entries(THEMES)\n          .map(\n            ([theme, prefix]) => `\n${prefix} [data-chart=${id}] {\n${colorConfig\n  .map(([key, itemConfig]) => {\n    const color =\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\n      itemConfig.color\n    return color ? `  --color-${key}: ${color};` : null\n  })\n  .join(\"\\n\")}\n}\n`\n          )\n          .join(\"\\n\"),\n      }}\n    />\n  )\n}\n\nconst ChartTooltip = RechartsPrimitive.Tooltip\n\nconst ChartTooltipContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\n    React.ComponentProps<\"div\"> & {\n      hideLabel?: boolean\n      hideIndicator?: boolean\n      indicator?: \"line\" | \"dot\" | \"dashed\"\n      nameKey?: string\n      labelKey?: string\n    }\n>(\n  (\n    {\n      active,\n      payload,\n      className,\n      indicator = \"dot\",\n      hideLabel = false,\n      hideIndicator = false,\n      label,\n      labelFormatter,\n      labelClassName,\n      formatter,\n      color,\n      nameKey,\n      labelKey,\n    },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    const tooltipLabel = React.useMemo(() => {\n      if (hideLabel || !payload?.length) {\n        return null\n      }\n\n      const [item] = payload\n      const key = `${labelKey || item.dataKey || item.name || \"value\"}`\n      const itemConfig = getPayloadConfigFromPayload(config, item, key)\n      const value =\n        !labelKey && typeof label === \"string\"\n          ? config[label as keyof typeof config]?.label || label\n          : itemConfig?.label\n\n      if (labelFormatter) {\n        return (\n          <div className={cn(\"font-medium\", labelClassName)}>\n            {labelFormatter(value, payload)}\n          </div>\n        )\n      }\n\n      if (!value) {\n        return null\n      }\n\n      return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\n    }, [\n      label,\n      labelFormatter,\n      payload,\n      hideLabel,\n      labelClassName,\n      config,\n      labelKey,\n    ])\n\n    if (!active || !payload?.length) {\n      return null\n    }\n\n    const nestLabel = payload.length === 1 && indicator !== \"dot\"\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl\",\n          className\n        )}\n      >\n        {!nestLabel ? tooltipLabel : null}\n        <div className=\"grid gap-1.5\">\n          {payload.map((item, index) => {\n            const key = `${nameKey || item.name || item.dataKey || \"value\"}`\n            const itemConfig = getPayloadConfigFromPayload(config, item, key)\n            const indicatorColor = color || item.payload.fill || item.color\n\n            return (\n              <div\n                key={item.dataKey}\n                className={cn(\n                  \"flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground\",\n                  indicator === \"dot\" && \"items-center\"\n                )}\n              >\n                {formatter && item?.value !== undefined && item.name ? (\n                  formatter(item.value, item.name, item, index, item.payload)\n                ) : (\n                  <>\n                    {itemConfig?.icon ? (\n                      <itemConfig.icon />\n                    ) : (\n                      !hideIndicator && (\n                        <div\n                          className={cn(\n                            \"shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]\",\n                            {\n                              \"h-2.5 w-2.5\": indicator === \"dot\",\n                              \"w-1\": indicator === \"line\",\n                              \"w-0 border-[1.5px] border-dashed bg-transparent\":\n                                indicator === \"dashed\",\n                              \"my-0.5\": nestLabel && indicator === \"dashed\",\n                            }\n                          )}\n                          style={\n                            {\n                              \"--color-bg\": indicatorColor,\n                              \"--color-border\": indicatorColor,\n                            } as React.CSSProperties\n                          }\n                        />\n                      )\n                    )}\n                    <div\n                      className={cn(\n                        \"flex flex-1 justify-between leading-none\",\n                        nestLabel ? \"items-end\" : \"items-center\"\n                      )}\n                    >\n                      <div className=\"grid gap-1.5\">\n                        {nestLabel ? tooltipLabel : null}\n                        <span className=\"text-muted-foreground\">\n                          {itemConfig?.label || item.name}\n                        </span>\n                      </div>\n                      {item.value && (\n                        <span className=\"font-mono font-medium tabular-nums text-foreground\">\n                          {item.value.toLocaleString()}\n                        </span>\n                      )}\n                    </div>\n                  </>\n                )}\n              </div>\n            )\n          })}\n        </div>\n      </div>\n    )\n  }\n)\nChartTooltipContent.displayName = \"ChartTooltip\"\n\nconst ChartLegend = RechartsPrimitive.Legend\n\nconst ChartLegendContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> &\n    Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\n      hideIcon?: boolean\n      nameKey?: string\n    }\n>(\n  (\n    { className, hideIcon = false, payload, verticalAlign = \"bottom\", nameKey },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    if (!payload?.length) {\n      return null\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"flex items-center justify-center gap-4\",\n          verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\n          className\n        )}\n      >\n        {payload.map((item) => {\n          const key = `${nameKey || item.dataKey || \"value\"}`\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\n\n          return (\n            <div\n              key={item.value}\n              className={cn(\n                \"flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground\"\n              )}\n            >\n              {itemConfig?.icon && !hideIcon ? (\n                <itemConfig.icon />\n              ) : (\n                <div\n                  className=\"h-2 w-2 shrink-0 rounded-[2px]\"\n                  style={{\n                    backgroundColor: item.color,\n                  }}\n                />\n              )}\n              {itemConfig?.label}\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n)\nChartLegendContent.displayName = \"ChartLegend\"\n\n// Helper to extract item config from a payload.\nfunction getPayloadConfigFromPayload(\n  config: ChartConfig,\n  payload: unknown,\n  key: string\n) {\n  if (typeof payload !== \"object\" || payload === null) {\n    return undefined\n  }\n\n  const payloadPayload =\n    \"payload\" in payload &&\n    typeof payload.payload === \"object\" &&\n    payload.payload !== null\n      ? payload.payload\n      : undefined\n\n  let configLabelKey: string = key\n\n  if (\n    key in payload &&\n    typeof payload[key as keyof typeof payload] === \"string\"\n  ) {\n    configLabelKey = payload[key as keyof typeof payload] as string\n  } else if (\n    payloadPayload &&\n    key in payloadPayload &&\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\n  ) {\n    configLabelKey = payloadPayload[\n      key as keyof typeof payloadPayload\n    ] as string\n  }\n\n  return configLabelKey in config\n    ? config[configLabelKey]\n    : config[key as keyof typeof config]\n}\n\nexport {\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  ChartStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAAA;AAAA;AAEA;;;AALA;;;;AAOA,uCAAuC;AACvC,MAAM,SAAS;IAAE,OAAO;IAAI,MAAM;AAAQ;AAgB1C,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAA4B;AAEnE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GARS;AAUT,MAAM,+BAAiB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,WAQpC,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE;;IAChD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAC3B,MAAM,UAAU,CAAC,MAAM,EAAE,MAAM,SAAS,OAAO,CAAC,MAAM,KAAK;IAE3D,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;QAAO;kBACrC,cAAA,6LAAC;YACC,cAAY;YACZ,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ypBACA;YAED,GAAG,KAAK;;8BAET,6LAAC;oBAAW,IAAI;oBAAS,QAAQ;;;;;;8BACjC,6LAAC,sKAAA,CAAA,sBAAqC;8BACnC;;;;;;;;;;;;;;;;;AAKX;;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAuC;IACrE,MAAM,cAAc,OAAO,OAAO,CAAC,QAAQ,MAAM,CAC/C,CAAC,GAAG,OAAO,GAAK,OAAO,KAAK,IAAI,OAAO,KAAK;IAG9C,IAAI,CAAC,YAAY,MAAM,EAAE;QACvB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,yBAAyB;YACvB,QAAQ,OAAO,OAAO,CAAC,QACpB,GAAG,CACF,CAAC,CAAC,OAAO,OAAO,GAAK,CAAC;AAClC,EAAE,OAAO,aAAa,EAAE,GAAG;AAC3B,EAAE,YACC,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW;oBACrB,MAAM,QACJ,WAAW,KAAK,EAAE,CAAC,MAAuC,IAC1D,WAAW,KAAK;oBAClB,OAAO,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,GAAG;gBACjD,GACC,IAAI,CAAC,MAAM;;AAEd,CAAC,EAEU,IAAI,CAAC;QACV;;;;;;AAGN;MA/BM;AAiCN,MAAM,eAAe,0JAAA,CAAA,UAAyB;AAE9C,MAAM,oCAAsB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAWzC,CACE,EACE,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,gBAAgB,KAAK,EACrB,KAAK,EACL,cAAc,EACd,cAAc,EACd,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EACT,EACD;;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qDAAE;YACjC,IAAI,aAAa,CAAC,SAAS,QAAQ;gBACjC,OAAO;YACT;YAEA,MAAM,CAAC,KAAK,GAAG;YACf,MAAM,MAAM,GAAG,YAAY,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,SAAS;YACjE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAC7D,MAAM,QACJ,CAAC,YAAY,OAAO,UAAU,WAC1B,MAAM,CAAC,MAA6B,EAAE,SAAS,QAC/C,YAAY;YAElB,IAAI,gBAAgB;gBAClB,qBACE,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;8BAC/B,eAAe,OAAO;;;;;;YAG7B;YAEA,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YAEA,qBAAO,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;0BAAkB;;;;;;QAC7D;oDAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,UAAU,CAAC,SAAS,QAAQ;QAC/B,OAAO;IACT;IAEA,MAAM,YAAY,QAAQ,MAAM,KAAK,KAAK,cAAc;IAExD,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0HACA;;YAGD,CAAC,YAAY,eAAe;0BAC7B,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,MAAM;oBAClB,MAAM,MAAM,GAAG,WAAW,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,SAAS;oBAChE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;oBAC7D,MAAM,iBAAiB,SAAS,KAAK,OAAO,CAAC,IAAI,IAAI,KAAK,KAAK;oBAE/D,qBACE,6LAAC;wBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA,cAAc,SAAS;kCAGxB,aAAa,MAAM,UAAU,aAAa,KAAK,IAAI,GAClD,UAAU,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,OAAO,KAAK,OAAO,kBAE1D;;gCACG,YAAY,qBACX,6LAAC,WAAW,IAAI;;;;2CAEhB,CAAC,+BACC,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;wCACE,eAAe,cAAc;wCAC7B,OAAO,cAAc;wCACrB,mDACE,cAAc;wCAChB,UAAU,aAAa,cAAc;oCACvC;oCAEF,OACE;wCACE,cAAc;wCACd,kBAAkB;oCACpB;;;;;;8CAKR,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4CACA,YAAY,cAAc;;sDAG5B,6LAAC;4CAAI,WAAU;;gDACZ,YAAY,eAAe;8DAC5B,6LAAC;oDAAK,WAAU;8DACb,YAAY,SAAS,KAAK,IAAI;;;;;;;;;;;;wCAGlC,KAAK,KAAK,kBACT,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;uBAhD/B,KAAK,OAAO;;;;;gBAwDvB;;;;;;;;;;;;AAIR;;QAzHqB;;;;QAAA;;;;AA2HvB,oBAAoB,WAAW,GAAG;AAElC,MAAM,cAAc,yJAAA,CAAA,SAAwB;AAE5C,MAAM,mCAAqB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAQxC,CACE,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,OAAO,EAAE,gBAAgB,QAAQ,EAAE,OAAO,EAAE,EAC3E;;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,kBAAkB,QAAQ,SAAS,QACnC;kBAGD,QAAQ,GAAG,CAAC,CAAC;YACZ,MAAM,MAAM,GAAG,WAAW,KAAK,OAAO,IAAI,SAAS;YACnD,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAE7D,qBACE,6LAAC;gBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;oBAGD,YAAY,QAAQ,CAAC,yBACpB,6LAAC,WAAW,IAAI;;;;6CAEhB,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,KAAK,KAAK;wBAC7B;;;;;;oBAGH,YAAY;;eAfR,KAAK,KAAK;;;;;QAkBrB;;;;;;AAGN;;QA1CqB;;;;QAAA;;;;AA4CvB,mBAAmB,WAAW,GAAG;AAEjC,gDAAgD;AAChD,SAAS,4BACP,MAAmB,EACnB,OAAgB,EAChB,GAAW;IAEX,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,OAAO;IACT;IAEA,MAAM,iBACJ,aAAa,WACb,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,KAAK,OAChB,QAAQ,OAAO,GACf;IAEN,IAAI,iBAAyB;IAE7B,IACE,OAAO,WACP,OAAO,OAAO,CAAC,IAA4B,KAAK,UAChD;QACA,iBAAiB,OAAO,CAAC,IAA4B;IACvD,OAAO,IACL,kBACA,OAAO,kBACP,OAAO,cAAc,CAAC,IAAmC,KAAK,UAC9D;QACA,iBAAiB,cAAc,CAC7B,IACD;IACH;IAEA,OAAO,kBAAkB,SACrB,MAAM,CAAC,eAAe,GACtB,MAAM,CAAC,IAA2B;AACxC", "debugId": null}}, {"offset": {"line": 5229, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/budget/BudgetVisualizer.tsx"], "sourcesContent": ["\n'use client';\n\nimport { Category } from '@/lib/types';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Progress } from '@/components/ui/progress';\nimport { Pie<PERSON><PERSON> } from 'lucide-react';\nimport { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from '@/components/ui/chart';\nimport { Pie, Cell, ResponsiveContainer, PieChart as RechartsPieChart } from 'recharts';\nimport type { ChartConfig } from '@/components/ui/chart';\n\ninterface BudgetVisualizerProps {\n  totalIncome: number;\n  categories: Category[];\n  balancesVisible: boolean; // Global balance visibility\n}\n\nconst chartColors = [\n  'hsl(var(--chart-1))',\n  'hsl(var(--chart-2))',\n  'hsl(var(--chart-3))',\n  'hsl(var(--chart-4))',\n  'hsl(var(--chart-5))',\n  'hsl(200 70% 50%)', \n  'hsl(300 70% 50%)',\n  'hsl(50 70% 50%)',\n];\n\nexport default function BudgetVisualizer({ totalIncome, categories, balancesVisible }: BudgetVisualizerProps) {\n  const overallTotalAllocated = categories.reduce((sum, cat) => sum + cat.budget, 0);\n  const unallocatedAmount = totalIncome - overallTotalAllocated;\n\n  const chartData = categories\n    .filter(cat => cat.budget > 0)\n    .map((cat, index) => ({\n      name: cat.name,\n      value: cat.budget,\n      fill: chartColors[index % chartColors.length],\n      isVisible: cat.isVisible ?? true, \n    }));\n\n  if (unallocatedAmount > 0) {\n    chartData.push({\n      name: 'Unallocated',\n      value: unallocatedAmount,\n      fill: 'hsl(var(--muted))',\n      isVisible: true, // Unallocated is always \"visible\" in terms of its value contributing\n    });\n  }\n\n  const chartConfig = chartData.reduce((acc, entry) => {\n    acc[entry.name] = {\n      label: entry.name,\n      color: entry.fill,\n    };\n    return acc;\n  }, {} as ChartConfig);\n\n\n  if (totalIncome === 0 && categories.length === 0) {\n    return (\n      <Card>\n        <CardHeader className=\"pb-2\">\n          <CardTitle className=\"text-lg flex items-center gap-2 font-headline\">\n            <PieChart className=\"h-5 w-5 text-primary\" />\n            Budget Overview\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <p className=\"text-sm text-muted-foreground text-center py-4\">Enter your income and add categories to see a visual breakdown.</p>\n        </CardContent>\n      </Card>\n    );\n  }\n  \n  const isEmptyChart = chartData.length === 0 || chartData.every(d => d.value === 0);\n\n  return (\n    <Card>\n      <CardHeader className=\"pb-2\">\n        <CardTitle className=\"text-lg flex items-center gap-2 font-headline\">\n          <PieChart className=\"h-5 w-5 text-primary\" />\n          Budget Overview\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        {isEmptyChart ? (\n           <p className=\"text-sm text-muted-foreground text-center py-4\">Allocate funds to categories to see the chart.</p>\n        ) : (\n          <ChartContainer config={chartConfig} className=\"mx-auto aspect-square max-h-[250px] h-auto\">\n            <RechartsPieChart>\n              <ChartTooltip \n                formatter={(value, name, props) => {\n                  // props.payload is the data item for the slice/tooltip entry\n                  const itemIsVisible = props.payload?.isVisible ?? true;\n                  const showDetails = balancesVisible && itemIsVisible;\n                  return [showDetails ? `R ${Number(value).toFixed(2)}` : \"R ••••\", name];\n                }}\n                content={<ChartTooltipContent nameKey=\"name\" hideLabel={false} />} // Always show name, formatter handles amount\n              />\n              <Pie \n                data={chartData} \n                dataKey=\"value\" \n                nameKey=\"name\" \n                cx=\"50%\" \n                cy=\"50%\" \n                outerRadius={80} \n                labelLine={false} \n                label={({ percent, payload }) => {\n                    const itemIsVisible = payload?.isVisible ?? true;\n                    const showDetails = balancesVisible && itemIsVisible;\n                    return showDetails ? `${(percent * 100).toFixed(0)}%` : \"\";\n                }}\n              >\n                {chartData.map((entry, index) => (\n                  <Cell key={`cell-${index}`} fill={entry.fill} />\n                ))}\n              </Pie>\n              <ChartLegend content={<ChartLegendContent nameKey=\"name\" className=\"text-xs flex-wrap justify-center gap-x-2 gap-y-1\" />} />\n            </RechartsPieChart>\n          </ChartContainer>\n        )}\n        {categories.map((category) => {\n          if (category.budget === 0) return null;\n          const displayCategoryDetails = balancesVisible && (category.isVisible ?? true);\n          const totalSubCategoryAllocation = category.subCategories.reduce((sum, sc) => sum + sc.allocatedAmount, 0);\n          const allocationPercentage = category.budget > 0 ? (totalSubCategoryAllocation / category.budget) * 100 : 0;\n          const isOverAllocated = totalSubCategoryAllocation > category.budget;\n          return (\n            <div key={category.id} className=\"mt-3 text-xs\">\n              <div className=\"flex justify-between items-center mb-0.5\">\n                <span className=\"font-medium\">{category.name}</span>\n                <span className=\"text-muted-foreground\">\n                  {displayCategoryDetails ? `${totalSubCategoryAllocation.toFixed(2)} / ${category.budget.toFixed(2)}` : '•••• / ••••'}\n                </span>\n              </div>\n              <Progress value={Math.min(allocationPercentage, 100)} className={`h-1.5 ${isOverAllocated ? 'bg-destructive/70 [&>*]:bg-destructive' : '[&>*]:bg-primary'}`} />\n            </div>\n          );\n        })}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAPA;;;;;;;AAgBA,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS,iBAAiB,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,EAAyB;IAC1G,MAAM,wBAAwB,WAAW,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,MAAM,EAAE;IAChF,MAAM,oBAAoB,cAAc;IAExC,MAAM,YAAY,WACf,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG,GAC3B,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;YACpB,MAAM,IAAI,IAAI;YACd,OAAO,IAAI,MAAM;YACjB,MAAM,WAAW,CAAC,QAAQ,YAAY,MAAM,CAAC;YAC7C,WAAW,IAAI,SAAS,IAAI;QAC9B,CAAC;IAEH,IAAI,oBAAoB,GAAG;QACzB,UAAU,IAAI,CAAC;YACb,MAAM;YACN,OAAO;YACP,MAAM;YACN,WAAW;QACb;IACF;IAEA,MAAM,cAAc,UAAU,MAAM,CAAC,CAAC,KAAK;QACzC,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG;YAChB,OAAO,MAAM,IAAI;YACjB,OAAO,MAAM,IAAI;QACnB;QACA,OAAO;IACT,GAAG,CAAC;IAGJ,IAAI,gBAAgB,KAAK,WAAW,MAAM,KAAK,GAAG;QAChD,qBACE,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAyB;;;;;;;;;;;;8BAIjD,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAE,WAAU;kCAAiD;;;;;;;;;;;;;;;;;IAItE;IAEA,MAAM,eAAe,UAAU,MAAM,KAAK,KAAK,UAAU,KAAK,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IAEhF,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAyB;;;;;;;;;;;;0BAIjD,6LAAC,mIAAA,CAAA,cAAW;;oBACT,6BACE,6LAAC;wBAAE,WAAU;kCAAiD;;;;;6CAE/D,6LAAC,oIAAA,CAAA,iBAAc;wBAAC,QAAQ;wBAAa,WAAU;kCAC7C,cAAA,6LAAC,uJAAA,CAAA,WAAgB;;8CACf,6LAAC,oIAAA,CAAA,eAAY;oCACX,WAAW,CAAC,OAAO,MAAM;wCACvB,6DAA6D;wCAC7D,MAAM,gBAAgB,MAAM,OAAO,EAAE,aAAa;wCAClD,MAAM,cAAc,mBAAmB;wCACvC,OAAO;4CAAC,cAAc,CAAC,EAAE,EAAE,OAAO,OAAO,OAAO,CAAC,IAAI,GAAG;4CAAU;yCAAK;oCACzE;oCACA,uBAAS,6LAAC,oIAAA,CAAA,sBAAmB;wCAAC,SAAQ;wCAAO,WAAW;;;;;;;;;;;8CAE1D,6LAAC,kJAAA,CAAA,MAAG;oCACF,MAAM;oCACN,SAAQ;oCACR,SAAQ;oCACR,IAAG;oCACH,IAAG;oCACH,aAAa;oCACb,WAAW;oCACX,OAAO,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE;wCACxB,MAAM,gBAAgB,SAAS,aAAa;wCAC5C,MAAM,cAAc,mBAAmB;wCACvC,OAAO,cAAc,GAAG,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;oCAC5D;8CAEC,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,6LAAC,uJAAA,CAAA,OAAI;4CAAuB,MAAM,MAAM,IAAI;2CAAjC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;8CAG9B,6LAAC,oIAAA,CAAA,cAAW;oCAAC,uBAAS,6LAAC,oIAAA,CAAA,qBAAkB;wCAAC,SAAQ;wCAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;oBAIxE,WAAW,GAAG,CAAC,CAAC;wBACf,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;wBAClC,MAAM,yBAAyB,mBAAmB,CAAC,SAAS,SAAS,IAAI,IAAI;wBAC7E,MAAM,6BAA6B,SAAS,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,KAAO,MAAM,GAAG,eAAe,EAAE;wBACxG,MAAM,uBAAuB,SAAS,MAAM,GAAG,IAAI,AAAC,6BAA6B,SAAS,MAAM,GAAI,MAAM;wBAC1G,MAAM,kBAAkB,6BAA6B,SAAS,MAAM;wBACpE,qBACE,6LAAC;4BAAsB,WAAU;;8CAC/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAe,SAAS,IAAI;;;;;;sDAC5C,6LAAC;4CAAK,WAAU;sDACb,yBAAyB,GAAG,2BAA2B,OAAO,CAAC,GAAG,GAAG,EAAE,SAAS,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG;;;;;;;;;;;;8CAG3G,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,OAAO,KAAK,GAAG,CAAC,sBAAsB;oCAAM,WAAW,CAAC,MAAM,EAAE,kBAAkB,2CAA2C,oBAAoB;;;;;;;2BAPnJ,SAAS,EAAE;;;;;oBAUzB;;;;;;;;;;;;;AAIR;KAnHwB", "debugId": null}}, {"offset": {"line": 5517, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5732, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/budget/GoalForm.tsx"], "sourcesContent": ["\n'use client';\n\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { useForm } from 'react-hook-form';\nimport * as z from 'zod';\nimport { Button } from '@/components/ui/button';\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '@/components/ui/form';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport type { FinancialGoal } from '@/lib/types';\nimport { Flag, ShoppingBag, Car, Home, Briefcase, GraduationCap, Heart, Plane, Gift } from 'lucide-react'; // Example icons\n\n// Add PiggyBank icon (not available in lucide-react by default, using a placeholder)\nconst PiggyBank = ({ className }: { className?: string }) => (\n  <svg className={className} xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n    <path d=\"M10 21h4\"/>\n    <path d=\"M12 17v4\"/>\n    <path d=\"M10 3h4c2.2 0 4 1.8 4 4v2c0 1.1-.9 2-2 2h-1\"/>\n    <path d=\"M8 11V7c0-2.2 1.8-4 4-4\"/>\n    <path d=\"M19 13c0-1.66-1.34-3-3-3h-2V7\"/>\n    <path d=\"M10 13c2.2 0 4-1.8 4-4\"/>\n    <path d=\"M2 13c2.5 0 2.5-3 5-3s2.5 3 5 3c2.5 0 2.5-3 5-3s2.5 3 5 3\"/>\n    <path d=\"M7.5 13s.5-1 2.5-1 2.5 1 2.5 1\"/>\n    <path d=\"M14 13c2 0 2.5-1 2.5-1\"/>\n    <path d=\"M2 17h.01\"/>\n  </svg>\n);\n\nconst goalFormSchema = z.object({\n  name: z.string().min(1, { message: 'Goal name is required.' }).max(50, { message: 'Name must be 50 characters or less.' }),\n  targetAmount: z.preprocess(\n    (val) => (typeof val === 'string' ? parseFloat(val) : val),\n    z.number().min(1, { message: 'Target amount must be greater than 0.' })\n  ),\n  icon: z.string().optional(),\n});\n\ntype GoalFormValues = z.infer<typeof goalFormSchema>;\n\ninterface GoalFormProps {\n  onSubmit: (name: string, targetAmount: number, icon?: string) => void;\n  initialData?: FinancialGoal | null;\n  onClose: () => void;\n}\n\n// For icon selection. In a real app, these might come from a config.\nconst availableIcons = [\n  { value: 'Default', label: 'Default', Icon: Flag },\n  { value: 'Savings', label: 'Savings', Icon: PiggyBank },\n  { value: 'Vacation', label: 'Vacation', Icon: Plane },\n  { value: 'Shopping', label: 'Shopping', Icon: ShoppingBag },\n  { value: 'Car', label: 'Car', Icon: Car },\n  { value: 'Home', label: 'Home Renovation', Icon: Home },\n  { value: 'Business', label: 'Business', Icon: Briefcase },\n  { value: 'Education', label: 'Education', Icon: GraduationCap },\n  { value: 'Wedding', label: 'Wedding', Icon: Heart },\n  { value: 'Gift', label: 'Gift', Icon: Gift },\n];\n\n\nexport default function GoalForm({ onSubmit, initialData, onClose }: GoalFormProps) {\n  const form = useForm<GoalFormValues>({\n    resolver: zodResolver(goalFormSchema),\n    defaultValues: {\n      name: initialData?.name || '',\n      targetAmount: initialData?.targetAmount || 0,\n      icon: initialData?.icon || 'Default',\n    },\n  });\n\n  const handleSubmit = (values: GoalFormValues) => {\n    onSubmit(values.name, values.targetAmount, values.icon);\n    if (!initialData) { // Only reset fully if it's a new goal\n      form.reset({ name: '', targetAmount: 0, icon: 'Default' });\n    }\n    onClose();\n  };\n\n  return (\n    <Form {...form}>\n      <form onSubmit={form.handleSubmit(handleSubmit)} className=\"space-y-4 pt-2\">\n        <FormField\n          control={form.control}\n          name=\"name\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel>Goal Name</FormLabel>\n              <FormControl>\n                <Input placeholder=\"e.g., New Laptop, Vacation Fund\" {...field} />\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n        <FormField\n          control={form.control}\n          name=\"targetAmount\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel>Target Amount (R)</FormLabel>\n              <FormControl>\n                <Input type=\"number\" placeholder=\"e.g., 15000\" {...field} step=\"any\" />\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n        <FormField\n          control={form.control}\n          name=\"icon\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel>Goal Icon (Optional)</FormLabel>\n              <Select onValueChange={field.onChange} defaultValue={field.value}>\n                <FormControl>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select an icon\" />\n                  </SelectTrigger>\n                </FormControl>\n                <SelectContent>\n                  {availableIcons.map(iconOpt => (\n                    <SelectItem key={iconOpt.value} value={iconOpt.value}>\n                      <div className=\"flex items-center gap-2\">\n                        <iconOpt.Icon className=\"h-4 w-4\" />\n                        {iconOpt.label}\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n        <div className=\"flex justify-end gap-2 pt-2\">\n          <Button type=\"button\" variant=\"outline\" onClick={onClose}>Cancel</Button>\n          <Button type=\"submit\">\n            {initialData ? 'Save Changes' : 'Set Goal'}\n          </Button>\n        </div>\n      </form>\n    </Form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AACA;AACA;AAQA;AACA;AAEA,sVAA2G,gBAAgB;AAA3H;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAjBA;;;;;;;;;AAmBA,qFAAqF;AACrF,MAAM,YAAY,CAAC,EAAE,SAAS,EAA0B,iBACtD,6LAAC;QAAI,WAAW;QAAW,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAY;QAAI,eAAc;QAAQ,gBAAe;;0BAC9L,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;0BACR,6LAAC;gBAAK,GAAE;;;;;;;;;;;;KAXN;AAeN,MAAM,iBAAiB,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,EAAE;IAC9B,MAAM,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAAyB,GAAG,GAAG,CAAC,IAAI;QAAE,SAAS;IAAsC;IACxH,cAAc,CAAA,GAAA,oJAAA,CAAA,aAAY,AAAD,EACvB,CAAC,MAAS,OAAO,QAAQ,WAAW,WAAW,OAAO,KACtD,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAAwC;IAEvE,MAAM,CAAA,GAAA,oJAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;AAC3B;AAUA,qEAAqE;AACrE,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAW,OAAO;QAAW,MAAM,qMAAA,CAAA,OAAI;IAAC;IACjD;QAAE,OAAO;QAAW,OAAO;QAAW,MAAM;IAAU;IACtD;QAAE,OAAO;QAAY,OAAO;QAAY,MAAM,uMAAA,CAAA,QAAK;IAAC;IACpD;QAAE,OAAO;QAAY,OAAO;QAAY,MAAM,uNAAA,CAAA,cAAW;IAAC;IAC1D;QAAE,OAAO;QAAO,OAAO;QAAO,MAAM,mMAAA,CAAA,MAAG;IAAC;IACxC;QAAE,OAAO;QAAQ,OAAO;QAAmB,MAAM,sMAAA,CAAA,OAAI;IAAC;IACtD;QAAE,OAAO;QAAY,OAAO;QAAY,MAAM,+MAAA,CAAA,YAAS;IAAC;IACxD;QAAE,OAAO;QAAa,OAAO;QAAa,MAAM,2NAAA,CAAA,gBAAa;IAAC;IAC9D;QAAE,OAAO;QAAW,OAAO;QAAW,MAAM,uMAAA,CAAA,QAAK;IAAC;IAClD;QAAE,OAAO;QAAQ,OAAO;QAAQ,MAAM,qMAAA,CAAA,OAAI;IAAC;CAC5C;AAGc,SAAS,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAiB;;IAChF,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAkB;QACnC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM,aAAa,QAAQ;YAC3B,cAAc,aAAa,gBAAgB;YAC3C,MAAM,aAAa,QAAQ;QAC7B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,SAAS,OAAO,IAAI,EAAE,OAAO,YAAY,EAAE,OAAO,IAAI;QACtD,IAAI,CAAC,aAAa;YAChB,KAAK,KAAK,CAAC;gBAAE,MAAM;gBAAI,cAAc;gBAAG,MAAM;YAAU;QAC1D;QACA;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YAAK,UAAU,KAAK,YAAY,CAAC;YAAe,WAAU;;8BACzD,6LAAC,mIAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;8CACP,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;wCAAC,aAAY;wCAAmC,GAAG,KAAK;;;;;;;;;;;8CAEhE,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAIlB,6LAAC,mIAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;8CACP,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;wCAAC,MAAK;wCAAS,aAAY;wCAAe,GAAG,KAAK;wCAAE,MAAK;;;;;;;;;;;8CAEjE,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAIlB,6LAAC,mIAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,mIAAA,CAAA,WAAQ;;8CACP,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCAAC,eAAe,MAAM,QAAQ;oCAAE,cAAc,MAAM,KAAK;;sDAC9D,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,qIAAA,CAAA,gBAAa;0DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;;;;;;sDAG7B,6LAAC,qIAAA,CAAA,gBAAa;sDACX,eAAe,GAAG,CAAC,CAAA,wBAClB,6LAAC,qIAAA,CAAA,aAAU;oDAAqB,OAAO,QAAQ,KAAK;8DAClD,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,QAAQ,IAAI;gEAAC,WAAU;;;;;;4DACvB,QAAQ,KAAK;;;;;;;mDAHD,QAAQ,KAAK;;;;;;;;;;;;;;;;8CASpC,6LAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8BAIlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,SAAQ;4BAAU,SAAS;sCAAS;;;;;;sCAC1D,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;sCACV,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;AAM5C;GAnFwB;;QACT,iKAAA,CAAA,UAAO;;;MADE", "debugId": null}}, {"offset": {"line": 6175, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/budget/FinancialGoalManager.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport type { FinancialGoal } from '@/lib/types';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { Progress } from '@/components/ui/progress';\nimport GoalForm from './GoalForm';\nimport { Target, Edit3, Trash2, PlusCircle, Flag, PiggyBank, Award, CheckCircle2 } from 'lucide-react';\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';\n\ninterface FinancialGoalManagerProps {\n  goal: FinancialGoal | null;\n  onSetGoal: (name: string, targetAmount: number, icon?: string) => void;\n  onUpdateProgress: (savedAmount: number) => void;\n  onClearGoal: () => void;\n  overallRemaining: number;\n  balancesVisible: boolean;\n}\n\nconst goalIcons: { [key: string]: React.ElementType } = {\n  Default: Flag,\n  Vacation: PiggyBank,\n  Gadget: Award,\n};\n\nexport default function FinancialGoalManager({ goal, onSetGoal, onUpdateProgress, onClearGoal, overallRemaining, balancesVisible }: FinancialGoalManagerProps) {\n  const [isGoalFormOpen, setIsGoalFormOpen] = useState(false);\n  const [isLogProgressFormOpen, setIsLogProgressFormOpen] = useState(false);\n  const [progressAmount, setProgressAmount] = useState<number>(0);\n  const [isClearGoalAlertOpen, setIsClearGoalAlertOpen] = useState(false);\n\n  useEffect(() => {\n    if (goal) {\n      setProgressAmount(goal.savedAmount);\n    }\n  }, [goal]);\n\n  const handleLogProgressSubmit = () => {\n    onUpdateProgress(progressAmount);\n    setIsLogProgressFormOpen(false);\n  };\n  \n  const formatCurrency = (amount: number) => {\n    if (!balancesVisible) return 'R ••••';\n    return `R ${amount.toFixed(2)}`;\n  };\n\n  const GoalIcon = goal?.icon && goalIcons[goal.icon] ? goalIcons[goal.icon] : Target;\n\n  if (!goal || goal.dateAchieved) {\n    return (\n      <Card>\n        <CardHeader className=\"pb-2\">\n          <CardTitle className=\"text-lg flex items-center gap-2 font-headline\">\n            {goal?.dateAchieved ? <CheckCircle2 className=\"h-5 w-5 text-green-500\" /> : <Target className=\"h-5 w-5 text-primary\" />}\n            {goal?.dateAchieved ? \"Goal Achieved!\" : \"Financial Goal\"}\n          </CardTitle>\n           {goal?.dateAchieved && goal && (\n             <CardDescription className=\"text-sm\">\n                Congrats on achieving: {goal.name}!\n             </CardDescription>\n           )}\n        </CardHeader>\n        <CardContent>\n            {goal?.dateAchieved && goal && (\n                 <div className=\"space-y-1 text-center\">\n                    <p className=\"text-2xl font-bold text-green-600\">{formatCurrency(goal.targetAmount)}</p>\n                    <p className=\"text-xs text-muted-foreground\">Achieved on {new Date(goal.dateAchieved).toLocaleDateString()}</p>\n                 </div>\n            )}\n          <p className={`text-sm text-muted-foreground ${goal?.dateAchieved ? 'mt-2 text-center' : 'text-center py-4'}`}>\n            {goal?.dateAchieved ? \"Ready for a new challenge?\" : \"Set a financial goal to start saving towards something important!\"}\n          </p>\n        </CardContent>\n        <CardFooter>\n          <Dialog open={isGoalFormOpen} onOpenChange={setIsGoalFormOpen}>\n            <DialogTrigger asChild>\n              <Button className=\"w-full\" onClick={() => setIsGoalFormOpen(true)}>\n                <PlusCircle className=\"mr-2 h-4 w-4\" /> {goal?.dateAchieved ? \"Set New Goal\" : \"Set a Goal\"}\n              </Button>\n            </DialogTrigger>\n            <DialogContent className=\"sm:max-w-[425px]\">\n              <DialogHeader>\n                <DialogTitle className=\"font-headline\">{goal?.dateAchieved ? \"Set New Financial Goal\" : \"Set Financial Goal\"}</DialogTitle>\n              </DialogHeader>\n              <GoalForm\n                onSubmit={(name, target, icon) => {\n                  onSetGoal(name, target, icon);\n                  setIsGoalFormOpen(false);\n                }}\n                initialData={null}\n                onClose={() => setIsGoalFormOpen(false)}\n              />\n            </DialogContent>\n          </Dialog>\n        </CardFooter>\n      </Card>\n    );\n  }\n  \n  const progressPercentage = goal.targetAmount > 0 ? (goal.savedAmount / goal.targetAmount) * 100 : 0;\n\n  return (\n    <Card>\n      <CardHeader className=\"pb-3 pt-4\">\n        <div className=\"flex items-center justify-between\">\n            <CardTitle className=\"text-lg flex items-center gap-2 font-headline\">\n                <GoalIcon className=\"h-5 w-5 text-primary\" />\n                {goal.name}\n            </CardTitle>\n            <div className=\"flex gap-1\">\n                <Dialog open={isGoalFormOpen} onOpenChange={setIsGoalFormOpen}>\n                    <DialogTrigger asChild>\n                        <Button variant=\"ghost\" size=\"icon\" className=\"h-7 w-7\" onClick={() => setIsGoalFormOpen(true)}>\n                            <Edit3 className=\"h-4 w-4\" />\n                        </Button>\n                    </DialogTrigger>\n                    <DialogContent className=\"sm:max-w-[425px]\">\n                        <DialogHeader><DialogTitle className=\"font-headline\">Edit Financial Goal</DialogTitle></DialogHeader>\n                        <GoalForm onSubmit={(name, target, icon) => { onSetGoal(name, target, icon); setIsGoalFormOpen(false); }} initialData={goal} onClose={() => setIsGoalFormOpen(false)} />\n                    </DialogContent>\n                </Dialog>\n                 <Button variant=\"ghost\" size=\"icon\" className=\"h-7 w-7 text-destructive hover:text-destructive\" onClick={() => setIsClearGoalAlertOpen(true)}>\n                    <Trash2 className=\"h-4 w-4\" />\n                 </Button>\n            </div>\n        </div>\n        <CardDescription className=\"text-xs pt-1\">Target: {formatCurrency(goal.targetAmount)} | Saved: {formatCurrency(goal.savedAmount)}</CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-3\">\n        <Progress value={progressPercentage} className=\"h-2 [&>*]:bg-primary\" />\n        <div className=\"text-xs text-muted-foreground\">\n            {formatCurrency(goal.targetAmount - goal.savedAmount)} still to go. You can do it!\n        </div>\n         {balancesVisible && overallRemaining > 0 && (\n            <p className=\"text-xs text-green-600 bg-green-500/10 p-1.5 rounded-md\">\n                You have {formatCurrency(overallRemaining)} unallocated in your budget. Consider putting some towards your goal!\n            </p>\n        )}\n      </CardContent>\n      <CardFooter>\n        <Dialog open={isLogProgressFormOpen} onOpenChange={setIsLogProgressFormOpen}>\n          <DialogTrigger asChild>\n            <Button className=\"w-full\" variant=\"outline\" onClick={() => { setProgressAmount(goal.savedAmount); setIsLogProgressFormOpen(true); }}>\n              Log Progress\n            </Button>\n          </DialogTrigger>\n          <DialogContent className=\"sm:max-w-[425px]\">\n            <DialogHeader>\n              <DialogTitle className=\"font-headline\">Log Progress for {goal.name}</DialogTitle>\n            </DialogHeader>\n            <div className=\"space-y-4 py-2\">\n                <p className=\"text-sm text-muted-foreground\">Current Target: {formatCurrency(goal.targetAmount)}</p>\n                <div>\n                    <label htmlFor=\"progressAmount\" className=\"block text-sm font-medium text-foreground mb-1\">Total Amount Saved Towards Goal (R)</label>\n                    <input\n                        id=\"progressAmount\"\n                        type={balancesVisible ? \"number\" : \"text\"}\n                        value={balancesVisible ? progressAmount : '••••'}\n                        onChange={(e) => {\n                            const val = parseFloat(e.target.value);\n                            setProgressAmount(Math.max(0, isNaN(val) ? 0 : val));\n                         }}\n                        readOnly={!balancesVisible}\n                        className=\"w-full p-2 border rounded-md border-input\"\n                        step=\"any\"\n                    />\n                </div>\n                <Button onClick={handleLogProgressSubmit} className=\"w-full\">Save Progress</Button>\n            </div>\n          </DialogContent>\n        </Dialog>\n      </CardFooter>\n\n      <AlertDialog open={isClearGoalAlertOpen} onOpenChange={setIsClearGoalAlertOpen}>\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle>Are you sure?</AlertDialogTitle>\n            <AlertDialogDescription>\n              This will clear your current financial goal \"{goal.name}\". This action cannot be undone.\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <AlertDialogFooter>\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\n            <AlertDialogAction\n              onClick={() => {\n                onClearGoal();\n                setIsClearGoalAlertOpen(false);\n              }}\n              className=\"bg-destructive hover:bg-destructive/90\"\n            >\n              Clear Goal\n            </AlertDialogAction>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAVA;;;;;;;;;AAqBA,MAAM,YAAkD;IACtD,SAAS,qMAAA,CAAA,OAAI;IACb,UAAU,mNAAA,CAAA,YAAS;IACnB,QAAQ,uMAAA,CAAA,QAAK;AACf;AAEe,SAAS,qBAAqB,EAAE,IAAI,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAA6B;;IAC3J,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,MAAM;gBACR,kBAAkB,KAAK,WAAW;YACpC;QACF;yCAAG;QAAC;KAAK;IAET,MAAM,0BAA0B;QAC9B,iBAAiB;QACjB,yBAAyB;IAC3B;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,iBAAiB,OAAO;QAC7B,OAAO,CAAC,EAAE,EAAE,OAAO,OAAO,CAAC,IAAI;IACjC;IAEA,MAAM,WAAW,MAAM,QAAQ,SAAS,CAAC,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,IAAI,CAAC,GAAG,yMAAA,CAAA,SAAM;IAEnF,IAAI,CAAC,QAAQ,KAAK,YAAY,EAAE;QAC9B,qBACE,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;gCAClB,MAAM,6BAAe,6LAAC,wNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;yDAA8B,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAC7F,MAAM,eAAe,mBAAmB;;;;;;;wBAEzC,MAAM,gBAAgB,sBACrB,6LAAC,mIAAA,CAAA,kBAAe;4BAAC,WAAU;;gCAAU;gCACV,KAAK,IAAI;gCAAC;;;;;;;;;;;;;8BAI1C,6LAAC,mIAAA,CAAA,cAAW;;wBACP,MAAM,gBAAgB,sBAClB,6LAAC;4BAAI,WAAU;;8CACZ,6LAAC;oCAAE,WAAU;8CAAqC,eAAe,KAAK,YAAY;;;;;;8CAClF,6LAAC;oCAAE,WAAU;;wCAAgC;wCAAa,IAAI,KAAK,KAAK,YAAY,EAAE,kBAAkB;;;;;;;;;;;;;sCAGlH,6LAAC;4BAAE,WAAW,CAAC,8BAA8B,EAAE,MAAM,eAAe,qBAAqB,oBAAoB;sCAC1G,MAAM,eAAe,+BAA+B;;;;;;;;;;;;8BAGzD,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAgB,cAAc;;0CAC1C,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;oCAAS,SAAS,IAAM,kBAAkB;;sDAC1D,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;wCAAE,MAAM,eAAe,iBAAiB;;;;;;;;;;;;0CAGnF,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,6LAAC,qIAAA,CAAA,eAAY;kDACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,WAAU;sDAAiB,MAAM,eAAe,2BAA2B;;;;;;;;;;;kDAE1F,6LAAC,2IAAA,CAAA,UAAQ;wCACP,UAAU,CAAC,MAAM,QAAQ;4CACvB,UAAU,MAAM,QAAQ;4CACxB,kBAAkB;wCACpB;wCACA,aAAa;wCACb,SAAS,IAAM,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO/C;IAEA,MAAM,qBAAqB,KAAK,YAAY,GAAG,IAAI,AAAC,KAAK,WAAW,GAAG,KAAK,YAAY,GAAI,MAAM;IAElG,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACjB,6LAAC;wCAAS,WAAU;;;;;;oCACnB,KAAK,IAAI;;;;;;;0CAEd,6LAAC;gCAAI,WAAU;;kDACX,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAM;wCAAgB,cAAc;;0DACxC,6LAAC,qIAAA,CAAA,gBAAa;gDAAC,OAAO;0DAClB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;oDAAU,SAAS,IAAM,kBAAkB;8DACrF,cAAA,6LAAC,6MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAGzB,6LAAC,qIAAA,CAAA,gBAAa;gDAAC,WAAU;;kEACrB,6LAAC,qIAAA,CAAA,eAAY;kEAAC,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,WAAU;sEAAgB;;;;;;;;;;;kEACrD,6LAAC,2IAAA,CAAA,UAAQ;wDAAC,UAAU,CAAC,MAAM,QAAQ;4DAAW,UAAU,MAAM,QAAQ;4DAAO,kBAAkB;wDAAQ;wDAAG,aAAa;wDAAM,SAAS,IAAM,kBAAkB;;;;;;;;;;;;;;;;;;kDAGrK,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAO,WAAU;wCAAkD,SAAS,IAAM,wBAAwB;kDACpI,cAAA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAI9B,6LAAC,mIAAA,CAAA,kBAAe;wBAAC,WAAU;;4BAAe;4BAAS,eAAe,KAAK,YAAY;4BAAE;4BAAW,eAAe,KAAK,WAAW;;;;;;;;;;;;;0BAEjI,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,OAAO;wBAAoB,WAAU;;;;;;kCAC/C,6LAAC;wBAAI,WAAU;;4BACV,eAAe,KAAK,YAAY,GAAG,KAAK,WAAW;4BAAE;;;;;;;oBAExD,mBAAmB,mBAAmB,mBACpC,6LAAC;wBAAE,WAAU;;4BAA0D;4BACzD,eAAe;4BAAkB;;;;;;;;;;;;;0BAIrD,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,MAAM;oBAAuB,cAAc;;sCACjD,6LAAC,qIAAA,CAAA,gBAAa;4BAAC,OAAO;sCACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,WAAU;gCAAS,SAAQ;gCAAU,SAAS;oCAAQ,kBAAkB,KAAK,WAAW;oCAAG,yBAAyB;gCAAO;0CAAG;;;;;;;;;;;sCAIxI,6LAAC,qIAAA,CAAA,gBAAa;4BAAC,WAAU;;8CACvB,6LAAC,qIAAA,CAAA,eAAY;8CACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;wCAAC,WAAU;;4CAAgB;4CAAkB,KAAK,IAAI;;;;;;;;;;;;8CAEpE,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;;gDAAgC;gDAAiB,eAAe,KAAK,YAAY;;;;;;;sDAC9F,6LAAC;;8DACG,6LAAC;oDAAM,SAAQ;oDAAiB,WAAU;8DAAiD;;;;;;8DAC3F,6LAAC;oDACG,IAAG;oDACH,MAAM,kBAAkB,WAAW;oDACnC,OAAO,kBAAkB,iBAAiB;oDAC1C,UAAU,CAAC;wDACP,MAAM,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wDACrC,kBAAkB,KAAK,GAAG,CAAC,GAAG,MAAM,OAAO,IAAI;oDAClD;oDACD,UAAU,CAAC;oDACX,WAAU;oDACV,MAAK;;;;;;;;;;;;sDAGb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAyB,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMvE,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAsB,cAAc;0BACrD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;;wCAAC;wCACwB,KAAK,IAAI;wCAAC;;;;;;;;;;;;;sCAG5D,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;wCACP;wCACA,wBAAwB;oCAC1B;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA7KwB;KAAA", "debugId": null}}, {"offset": {"line": 6808, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/floating-action-button.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Plus, X, Wallet, Target, PieChart, TrendingUp } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\ninterface FABAction {\n  icon: React.ComponentType<{ className?: string }>;\n  label: string;\n  onClick: () => void;\n  color?: string;\n}\n\ninterface FloatingActionButtonProps {\n  actions?: FABAction[];\n}\n\nconst defaultActions: FABAction[] = [\n  {\n    icon: Wallet,\n    label: 'Add Income',\n    onClick: () => console.log('Add Income'),\n    color: 'from-primary to-primary/80',\n  },\n  {\n    icon: PieChart,\n    label: 'New Category',\n    onClick: () => console.log('New Category'),\n    color: 'from-accent to-accent/80',\n  },\n  {\n    icon: Target,\n    label: 'Set Goal',\n    onClick: () => console.log('Set Goal'),\n    color: 'from-success to-success/80',\n  },\n  {\n    icon: TrendingUp,\n    label: 'Quick Transaction',\n    onClick: () => console.log('Quick Transaction'),\n    color: 'from-warning to-warning/80',\n  },\n];\n\nexport default function FloatingActionButton({ actions = defaultActions }: FloatingActionButtonProps) {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const toggleOpen = () => setIsOpen(!isOpen);\n\n  return (\n    <div className=\"fixed bottom-6 right-6 z-50\">\n      {/* Action Items */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            className=\"absolute bottom-16 right-0 space-y-3\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.2 }}\n          >\n            {actions.map((action, index) => {\n              const Icon = action.icon;\n              return (\n                <motion.div\n                  key={action.label}\n                  initial={{ opacity: 0, y: 20, scale: 0.8 }}\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\n                  exit={{ opacity: 0, y: 20, scale: 0.8 }}\n                  transition={{\n                    duration: 0.2,\n                    delay: index * 0.05,\n                    type: 'spring',\n                    stiffness: 300,\n                    damping: 20,\n                  }}\n                  className=\"flex items-center gap-3\"\n                >\n                  {/* Label */}\n                  <motion.div\n                    className=\"bg-card/90 backdrop-blur-sm border border-white/10 px-3 py-2 rounded-lg shadow-lg\"\n                    whileHover={{ scale: 1.05 }}\n                  >\n                    <span className=\"text-sm font-medium text-foreground whitespace-nowrap\">\n                      {action.label}\n                    </span>\n                  </motion.div>\n\n                  {/* Action Button */}\n                  <motion.button\n                    onClick={() => {\n                      action.onClick();\n                      setIsOpen(false);\n                    }}\n                    className={`w-12 h-12 bg-gradient-to-r ${action.color || 'from-primary to-primary/80'} rounded-full shadow-lg flex items-center justify-center text-white`}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    transition={{ type: 'spring', stiffness: 400, damping: 17 }}\n                  >\n                    <Icon className=\"w-5 h-5\" />\n                  </motion.button>\n                </motion.div>\n              );\n            })}\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Main FAB */}\n      <motion.button\n        onClick={toggleOpen}\n        className=\"fab\"\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n        animate={{ rotate: isOpen ? 45 : 0 }}\n        transition={{ type: 'spring', stiffness: 400, damping: 17 }}\n      >\n        <motion.div\n          animate={{ rotate: isOpen ? 45 : 0 }}\n          transition={{ duration: 0.2 }}\n        >\n          {isOpen ? <X className=\"w-6 h-6\" /> : <Plus className=\"w-6 h-6\" />}\n        </motion.div>\n      </motion.button>\n\n      {/* Backdrop */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            className=\"fixed inset-0 bg-black/20 backdrop-blur-sm -z-10\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            onClick={() => setIsOpen(false)}\n          />\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n\n// Preset FAB configurations\nexport const IncomeFAB = () => (\n  <FloatingActionButton\n    actions={[\n      {\n        icon: Wallet,\n        label: 'Add Salary',\n        onClick: () => console.log('Add Salary'),\n        color: 'from-primary to-primary/80',\n      },\n      {\n        icon: TrendingUp,\n        label: 'Add Bonus',\n        onClick: () => console.log('Add Bonus'),\n        color: 'from-success to-success/80',\n      },\n    ]}\n  />\n);\n\nexport const BudgetFAB = () => (\n  <FloatingActionButton\n    actions={[\n      {\n        icon: PieChart,\n        label: 'New Category',\n        onClick: () => console.log('New Category'),\n        color: 'from-accent to-accent/80',\n      },\n      {\n        icon: Target,\n        label: 'Set Budget',\n        onClick: () => console.log('Set Budget'),\n        color: 'from-primary to-primary/80',\n      },\n      {\n        icon: TrendingUp,\n        label: 'Add Expense',\n        onClick: () => console.log('Add Expense'),\n        color: 'from-warning to-warning/80',\n      },\n    ]}\n  />\n);\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAkBA,MAAM,iBAA8B;IAClC;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC3B,OAAO;IACT;IACA;QACE,MAAM,iNAAA,CAAA,WAAQ;QACd,OAAO;QACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC3B,OAAO;IACT;IACA;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC3B,OAAO;IACT;IACA;QACE,MAAM,qNAAA,CAAA,aAAU;QAChB,OAAO;QACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC3B,OAAO;IACT;CACD;AAEc,SAAS,qBAAqB,EAAE,UAAU,cAAc,EAA6B;;IAClG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa,IAAM,UAAU,CAAC;IAEpC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;8BAE3B,QAAQ,GAAG,CAAC,CAAC,QAAQ;wBACpB,MAAM,OAAO,OAAO,IAAI;wBACxB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAI,OAAO;4BAAI;4BACzC,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAG,OAAO;4BAAE;4BACtC,MAAM;gCAAE,SAAS;gCAAG,GAAG;gCAAI,OAAO;4BAAI;4BACtC,YAAY;gCACV,UAAU;gCACV,OAAO,QAAQ;gCACf,MAAM;gCACN,WAAW;gCACX,SAAS;4BACX;4BACA,WAAU;;8CAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;8CAE1B,cAAA,6LAAC;wCAAK,WAAU;kDACb,OAAO,KAAK;;;;;;;;;;;8CAKjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCACP,OAAO,OAAO;wCACd,UAAU;oCACZ;oCACA,WAAW,CAAC,2BAA2B,EAAE,OAAO,KAAK,IAAI,6BAA6B,mEAAmE,CAAC;oCAC1J,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;oCACvB,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;8CAE1D,cAAA,6LAAC;wCAAK,WAAU;;;;;;;;;;;;2BAlCb,OAAO,KAAK;;;;;oBAsCvB;;;;;;;;;;;0BAMN,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;gBACvB,SAAS;oBAAE,QAAQ,SAAS,KAAK;gBAAE;gBACnC,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;gBAAG;0BAE1D,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,QAAQ,SAAS,KAAK;oBAAE;oBACnC,YAAY;wBAAE,UAAU;oBAAI;8BAE3B,uBAAS,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAAe,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAK1D,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,SAAS,IAAM,UAAU;;;;;;;;;;;;;;;;;AAMrC;GA/FwB;KAAA;AAkGjB,MAAM,YAAY,kBACvB,6LAAC;QACC,SAAS;YACP;gBACE,MAAM,yMAAA,CAAA,SAAM;gBACZ,OAAO;gBACP,SAAS,IAAM,QAAQ,GAAG,CAAC;gBAC3B,OAAO;YACT;YACA;gBACE,MAAM,qNAAA,CAAA,aAAU;gBAChB,OAAO;gBACP,SAAS,IAAM,QAAQ,GAAG,CAAC;gBAC3B,OAAO;YACT;SACD;;;;;;MAfQ;AAmBN,MAAM,YAAY,kBACvB,6LAAC;QACC,SAAS;YACP;gBACE,MAAM,iNAAA,CAAA,WAAQ;gBACd,OAAO;gBACP,SAAS,IAAM,QAAQ,GAAG,CAAC;gBAC3B,OAAO;YACT;YACA;gBACE,MAAM,yMAAA,CAAA,SAAM;gBACZ,OAAO;gBACP,SAAS,IAAM,QAAQ,GAAG,CAAC;gBAC3B,OAAO;YACT;YACA;gBACE,MAAM,qNAAA,CAAA,aAAU;gBAChB,OAAO;gBACP,SAAS,IAAM,QAAQ,GAAG,CAAC;gBAC3B,OAAO;YACT;SACD;;;;;;MArBQ", "debugId": null}}, {"offset": {"line": 7108, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/app/page.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { useRouter } from 'next/navigation';\nimport type { Category, SubCategory, BudgetData, FinancialGoal } from '@/lib/types';\nimport ModernLayout from '@/components/layout/ModernLayout';\nimport { QuickStatsGrid } from '@/components/dashboard/ModernMetricCard';\nimport CategoryManager from '@/components/budget/CategoryManager';\nimport BudgetVisualizer from '@/components/budget/BudgetVisualizer';\nimport FinancialGoalManager from '@/components/budget/FinancialGoalManager';\nimport { useToast } from \"@/hooks/use-toast\";\nimport { useAuth } from '@/context/AuthContext';\nimport { WalletCards, Target, Trophy, Plus, TrendingUp, Pie<PERSON><PERSON>, BarChart3 } from 'lucide-react';\nimport { AnimatedCard } from '@/components/ui/animated-wrapper';\nimport { motion } from 'framer-motion';\nimport { Button } from '@/components/ui/button';\nimport { CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport FloatingActionButton from '@/components/ui/floating-action-button';\n\nconst ACHIEVEMENT_IDS = {\n  BUDGET_STARTER: 'BUDGET_STARTER',\n  GOAL_SETTER: 'GOAL_SETTER',\n  GOAL_CRUSHER: 'GOAL_CRUSHER',\n};\n\nconst ACHIEVEMENT_DETAILS: Record<string, { title: string; description: string; IconComponent: React.ElementType }> = {\n  [ACHIEVEMENT_IDS.BUDGET_STARTER]: { title: \"Budget Starter!\", description: \"You've set your income and added your first category!\", IconComponent: WalletCards },\n  [ACHIEVEMENT_IDS.GOAL_SETTER]: { title: \"Goal Setter!\", description: \"You've set your first financial goal!\", IconComponent: Target },\n  [ACHIEVEMENT_IDS.GOAL_CRUSHER]: { title: \"Goal Crusher!\", description: \"Congratulations! You've achieved your financial goal!\", IconComponent: Trophy },\n};\n\n\nexport default function BudgetPage() {\n  const { currentUser, loading: authLoading } = useAuth();\n  const router = useRouter();\n\n  const [isMounted, setIsMounted] = useState(false);\n  const [totalIncome, setTotalIncome] = useState<number>(0);\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [financialGoal, setFinancialGoal] = useState<FinancialGoal | null>(null);\n  const [achievements, setAchievements] = useState<string[]>([]);\n  const [balancesVisible, setBalancesVisible] = useState<boolean>(true);\n  const { toast } = useToast();\n\n  const getLocalStorageKey = useCallback(() => {\n    if (currentUser) {\n      return `budgetWiseData_${currentUser.uid}`;\n    }\n    return null; // Or a default key if you want non-authenticated users to have some data (not recommended for this app)\n  }, [currentUser]);\n\n\n  const awardAchievement = useCallback((achievementId: string) => {\n    if (!achievements.includes(achievementId)) {\n      setAchievements(prev => [...prev, achievementId]);\n      const details = ACHIEVEMENT_DETAILS[achievementId];\n      if (details) {\n        toast({\n          title: `🏆 ${details.title}`,\n          description: details.description,\n        });\n      }\n    }\n  }, [achievements, toast]);\n\n  useEffect(() => {\n    setIsMounted(true); // Component is mounted on client\n  }, []);\n\n  useEffect(() => {\n    if (authLoading) return; // Wait for auth state to be determined\n\n    if (!currentUser) {\n      router.push('/login');\n      return;\n    }\n\n    const storageKey = getLocalStorageKey();\n    if (!storageKey) return; // Should not happen if currentUser is present\n\n    if (isMounted) { // Ensure component is client-side mounted before accessing localStorage\n      const storedData = localStorage.getItem(storageKey);\n      if (storedData) {\n        try {\n          const parsedData: BudgetData = JSON.parse(storedData);\n          setTotalIncome(parsedData.totalIncome || 0);\n          setCategories(\n            (parsedData.categories || []).map(cat => ({\n              ...cat,\n              isVisible: cat.isVisible !== undefined ? cat.isVisible : true,\n              subCategories: cat.subCategories || [],\n            }))\n          );\n          setFinancialGoal(parsedData.financialGoal || null);\n          setAchievements(parsedData.achievements || []);\n          setBalancesVisible(parsedData.balancesVisible !== undefined ? parsedData.balancesVisible : true);\n        } catch (error) {\n          console.error(\"Failed to parse budget data from localStorage\", error);\n          // Reset to defaults if parsing fails\n          setTotalIncome(0);\n          setCategories([]);\n          setFinancialGoal(null);\n          setAchievements([]);\n          setBalancesVisible(true);\n        }\n      } else {\n        // No data found, initialize with defaults\n        setTotalIncome(0);\n        setCategories([]);\n        setFinancialGoal(null);\n        setAchievements([]);\n        setBalancesVisible(true);\n      }\n    }\n  }, [currentUser, authLoading, router, isMounted, getLocalStorageKey]);\n\n  useEffect(() => {\n    const storageKey = getLocalStorageKey();\n    if (!storageKey || !isMounted || authLoading || !currentUser) return; // Only save if user is logged in and component is mounted\n\n    const budgetData: BudgetData = { totalIncome, categories, financialGoal, achievements, balancesVisible };\n    localStorage.setItem(storageKey, JSON.stringify(budgetData));\n\n    if (totalIncome > 0 && categories.length > 0) {\n      awardAchievement(ACHIEVEMENT_IDS.BUDGET_STARTER);\n    }\n    if (financialGoal?.dateAchieved) {\n      awardAchievement(ACHIEVEMENT_IDS.GOAL_CRUSHER);\n    }\n  }, [totalIncome, categories, financialGoal, achievements, balancesVisible, isMounted, awardAchievement, getLocalStorageKey, authLoading, currentUser]);\n\n  const toggleBalancesVisibility = useCallback(() => {\n    setBalancesVisible(prev => !prev);\n  }, []);\n\n  const toggleCategoryVisibility = useCallback((categoryId: string) => {\n    setCategories(prevCategories =>\n      prevCategories.map(cat =>\n        cat.id === categoryId ? { ...cat, isVisible: !(cat.isVisible ?? true) } : cat\n      )\n    );\n  }, []);\n\n\n\n  const addCategory = useCallback((name: string, budget: number) => {\n    const newCategory: Category = {\n      id: crypto.randomUUID(),\n      name,\n      budget,\n      subCategories: [],\n      isVisible: true,\n    };\n    setCategories((prev) => [...prev, newCategory]);\n  }, []);\n\n  const updateCategory = useCallback((id: string, newName: string, newBudget: number) => {\n    setCategories((prev) =>\n      prev.map((cat) =>\n        cat.id === id ? { ...cat, name: newName, budget: newBudget } : cat\n      )\n    );\n  }, []);\n\n  const deleteCategory = useCallback((id: string) => {\n    setCategories((prev) => prev.filter((cat) => cat.id !== id));\n  }, []);\n\n  const addSubCategory = useCallback((parentId: string, name: string, allocatedAmount: number): boolean => {\n    let success = false;\n    setCategories((prev) =>\n      prev.map((cat) => {\n        if (cat.id === parentId) {\n          const currentSubTotal = cat.subCategories.reduce((sum, sc) => sum + sc.allocatedAmount, 0);\n          if (currentSubTotal + allocatedAmount > cat.budget) {\n            success = false;\n            return cat;\n          }\n          const newSubCategory: SubCategory = {\n            id: crypto.randomUUID(),\n            name,\n            allocatedAmount,\n          };\n          success = true;\n          return { ...cat, subCategories: [...cat.subCategories, newSubCategory] };\n        }\n        return cat;\n      })\n    );\n    return success;\n  }, []);\n\n  const updateSubCategory = useCallback((parentId: string, subId: string, newName: string, newAllocatedAmount: number): boolean => {\n    let success = false;\n    setCategories((prev) =>\n      prev.map((cat) => {\n        if (cat.id === parentId) {\n          const otherSubCategoriesTotal = cat.subCategories\n            .filter(sc => sc.id !== subId)\n            .reduce((sum, sc) => sum + sc.allocatedAmount, 0);\n          \n          if (otherSubCategoriesTotal + newAllocatedAmount > cat.budget) {\n            success = false;\n            return cat;\n          }\n          success = true;\n          return {\n            ...cat,\n            subCategories: cat.subCategories.map((sc) =>\n              sc.id === subId ? { ...sc, name: newName, allocatedAmount: newAllocatedAmount } : sc\n            ),\n          };\n        }\n        return cat;\n      })\n    );\n    return success;\n  }, []);\n\n  const deleteSubCategory = useCallback((parentId: string, subId: string) => {\n    setCategories((prev) =>\n      prev.map((cat) =>\n        cat.id === parentId\n          ? { ...cat, subCategories: cat.subCategories.filter((sc) => sc.id !== subId) }\n          : cat\n      )\n    );\n    toast({ title: \"Subcategory Deleted\", description: \"Subcategory has been removed.\"});\n  }, [toast]);\n\n  const handleSetFinancialGoal = useCallback((name: string, targetAmount: number, icon?: string) => {\n    const newGoal: FinancialGoal = {\n      id: financialGoal?.id || crypto.randomUUID(),\n      name,\n      targetAmount,\n      savedAmount: financialGoal?.id ? financialGoal.savedAmount : 0,\n      icon,\n      dateSet: financialGoal?.dateSet || new Date().toISOString(),\n      dateAchieved: null,\n    };\n    setFinancialGoal(newGoal);\n    if (!achievements.includes(ACHIEVEMENT_IDS.GOAL_SETTER) || !financialGoal) {\n        awardAchievement(ACHIEVEMENT_IDS.GOAL_SETTER);\n    }\n    toast({ title: \"Financial Goal Updated!\", description: `Your goal \"${name}\" has been set/updated.`});\n  }, [financialGoal, awardAchievement, toast, achievements]);\n\n  const handleUpdateGoalProgress = useCallback((savedAmount: number) => {\n    if (financialGoal) {\n      const updatedGoal = { ...financialGoal, savedAmount };\n      if (savedAmount >= financialGoal.targetAmount && !financialGoal.dateAchieved) {\n        updatedGoal.dateAchieved = new Date().toISOString();\n        awardAchievement(ACHIEVEMENT_IDS.GOAL_CRUSHER);\n        toast({ title: \"Goal Achieved!\", description: `Congratulations on reaching your goal: ${financialGoal.name}!`, duration: 5000 });\n      }\n      setFinancialGoal(updatedGoal);\n    }\n  }, [financialGoal, awardAchievement, toast]);\n  \n  const handleClearGoal = useCallback(() => {\n    setFinancialGoal(null);\n    toast({ title: \"Financial Goal Cleared\", description: \"Your financial goal has been removed.\"});\n  }, [toast]);\n\n  const overallTotalAllocated = categories.reduce((sum, cat) => sum + cat.budget, 0);\n  const overallRemaining = totalIncome - overallTotalAllocated;\n\n  if (authLoading || !isMounted || !currentUser) {\n    return (\n      <ModernLayout\n        balancesVisible={balancesVisible}\n        onToggleBalances={toggleBalancesVisibility}\n      >\n        <div className=\"flex items-center justify-center min-h-[60vh]\">\n          <div className=\"text-center space-y-4\">\n            <motion.div\n              className=\"w-16 h-16 border-4 border-primary border-t-transparent rounded-full mx-auto\"\n              animate={{ rotate: 360 }}\n              transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n            />\n            <p className=\"text-muted-foreground\">Loading your budget dashboard...</p>\n          </div>\n        </div>\n      </ModernLayout>\n    );\n  }\n\n  return (\n    <ModernLayout\n      balancesVisible={balancesVisible}\n      onToggleBalances={toggleBalancesVisibility}\n    >\n      <div className=\"space-y-8\">\n        {/* Page Header */}\n        <motion.div\n          className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <div>\n            <h1 className=\"text-3xl font-bold text-foreground\">Dashboard</h1>\n            <p className=\"text-muted-foreground\">\n              Welcome back! Here's your financial overview.\n            </p>\n          </div>\n\n          <div className=\"flex items-center gap-3\">\n            <Button variant=\"outline\" className=\"glass-card border-white/20\">\n              <BarChart3 className=\"w-4 h-4 mr-2\" />\n              View Reports\n            </Button>\n            <Button className=\"btn-modern-primary\">\n              <Plus className=\"w-4 h-4 mr-2\" />\n              Quick Add\n            </Button>\n          </div>\n        </motion.div>\n\n        {/* Quick Stats */}\n        <QuickStatsGrid\n          totalIncome={totalIncome}\n          totalAllocated={overallTotalAllocated}\n          remaining={overallRemaining}\n          savingsGoal={financialGoal?.targetAmount}\n          balancesVisible={balancesVisible}\n        />\n\n        {/* Main Content Grid */}\n        <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-8\">\n          {/* Left Column - Charts and Visualizations */}\n          <div className=\"xl:col-span-2 space-y-6\">\n            {/* Budget Visualizer */}\n            <AnimatedCard delay={0.2} className=\"chart-container\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl flex items-center justify-center\">\n                    <PieChart className=\"w-5 h-5 text-primary\" />\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold\">Budget Overview</h3>\n                    <p className=\"text-sm text-muted-foreground\">Category allocation breakdown</p>\n                  </div>\n                </div>\n                <Button variant=\"ghost\" size=\"sm\" className=\"glass-card\">\n                  <TrendingUp className=\"w-4 h-4 mr-2\" />\n                  Analyze\n                </Button>\n              </div>\n              <BudgetVisualizer\n                totalIncome={totalIncome}\n                categories={categories}\n                balancesVisible={balancesVisible}\n              />\n            </AnimatedCard>\n\n            {/* Category Manager */}\n            <AnimatedCard delay={0.3} className=\"glass-card\">\n              <CategoryManager\n                categories={categories}\n                onAddCategory={addCategory}\n                onUpdateCategory={updateCategory}\n                onDeleteCategory={deleteCategory}\n                onAddSubCategory={addSubCategory}\n                onUpdateSubCategory={updateSubCategory}\n                onDeleteSubCategory={deleteSubCategory}\n                onToggleCategoryVisibility={toggleCategoryVisibility}\n                balancesVisible={balancesVisible}\n              />\n            </AnimatedCard>\n          </div>\n\n          {/* Right Column - Goals and Quick Actions */}\n          <div className=\"space-y-6\">\n            {/* Financial Goals */}\n            <AnimatedCard delay={0.4} className=\"glass-card\">\n              <FinancialGoalManager\n                goal={financialGoal}\n                onSetGoal={handleSetFinancialGoal}\n                onUpdateProgress={handleUpdateGoalProgress}\n                onClearGoal={handleClearGoal}\n                overallRemaining={overallRemaining}\n                balancesVisible={balancesVisible}\n              />\n            </AnimatedCard>\n\n            {/* Quick Actions */}\n            <AnimatedCard delay={0.5} className=\"glass-card\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <Target className=\"w-5 h-5 text-primary\" />\n                  Quick Actions\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-3\">\n                <Button variant=\"outline\" className=\"w-full justify-start glass-card border-white/20\">\n                  <Plus className=\"w-4 h-4 mr-2\" />\n                  Add Income Source\n                </Button>\n                <Button variant=\"outline\" className=\"w-full justify-start glass-card border-white/20\">\n                  <Target className=\"w-4 h-4 mr-2\" />\n                  Set New Goal\n                </Button>\n                <Button variant=\"outline\" className=\"w-full justify-start glass-card border-white/20\">\n                  <Trophy className=\"w-4 h-4 mr-2\" />\n                  View Achievements\n                </Button>\n              </CardContent>\n            </AnimatedCard>\n\n            {/* Recent Activity */}\n            <AnimatedCard delay={0.6} className=\"glass-card\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <TrendingUp className=\"w-5 h-5 text-primary\" />\n                  Recent Activity\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center gap-3 p-3 rounded-lg bg-white/5\">\n                    <div className=\"w-2 h-2 bg-success rounded-full\" />\n                    <div className=\"flex-1\">\n                      <p className=\"text-sm font-medium\">Budget updated</p>\n                      <p className=\"text-xs text-muted-foreground\">2 minutes ago</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center gap-3 p-3 rounded-lg bg-white/5\">\n                    <div className=\"w-2 h-2 bg-primary rounded-full\" />\n                    <div className=\"flex-1\">\n                      <p className=\"text-sm font-medium\">New category added</p>\n                      <p className=\"text-xs text-muted-foreground\">1 hour ago</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center gap-3 p-3 rounded-lg bg-white/5\">\n                    <div className=\"w-2 h-2 bg-accent rounded-full\" />\n                    <div className=\"flex-1\">\n                      <p className=\"text-sm font-medium\">Goal achieved</p>\n                      <p className=\"text-xs text-muted-foreground\">Yesterday</p>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </AnimatedCard>\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Action Button */}\n      <FloatingActionButton />\n    </ModernLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAjBA;;;;;;;;;;;;;;;;AAmBA,MAAM,kBAAkB;IACtB,gBAAgB;IAChB,aAAa;IACb,cAAc;AAChB;AAEA,MAAM,sBAAgH;IACpH,CAAC,gBAAgB,cAAc,CAAC,EAAE;QAAE,OAAO;QAAmB,aAAa;QAAyD,eAAe,uNAAA,CAAA,cAAW;IAAC;IAC/J,CAAC,gBAAgB,WAAW,CAAC,EAAE;QAAE,OAAO;QAAgB,aAAa;QAAyC,eAAe,yMAAA,CAAA,SAAM;IAAC;IACpI,CAAC,gBAAgB,YAAY,CAAC,EAAE;QAAE,OAAO;QAAiB,aAAa;QAAyD,eAAe,yMAAA,CAAA,SAAM;IAAC;AACxJ;AAGe,SAAS;;IACtB,MAAM,EAAE,WAAW,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACpD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACrC,IAAI,aAAa;gBACf,OAAO,CAAC,eAAe,EAAE,YAAY,GAAG,EAAE;YAC5C;YACA,OAAO,MAAM,wGAAwG;QACvH;qDAAG;QAAC;KAAY;IAGhB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YACpC,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAgB;gBACzC;gEAAgB,CAAA,OAAQ;+BAAI;4BAAM;yBAAc;;gBAChD,MAAM,UAAU,mBAAmB,CAAC,cAAc;gBAClD,IAAI,SAAS;oBACX,MAAM;wBACJ,OAAO,CAAC,GAAG,EAAE,QAAQ,KAAK,EAAE;wBAC5B,aAAa,QAAQ,WAAW;oBAClC;gBACF;YACF;QACF;mDAAG;QAAC;QAAc;KAAM;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,aAAa,OAAO,iCAAiC;QACvD;+BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,aAAa,QAAQ,uCAAuC;YAEhE,IAAI,CAAC,aAAa;gBAChB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,MAAM,aAAa;YACnB,IAAI,CAAC,YAAY,QAAQ,8CAA8C;YAEvE,IAAI,WAAW;gBACb,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,IAAI;wBACF,MAAM,aAAyB,KAAK,KAAK,CAAC;wBAC1C,eAAe,WAAW,WAAW,IAAI;wBACzC,cACE,CAAC,WAAW,UAAU,IAAI,EAAE,EAAE,GAAG;oDAAC,CAAA,MAAO,CAAC;oCACxC,GAAG,GAAG;oCACN,WAAW,IAAI,SAAS,KAAK,YAAY,IAAI,SAAS,GAAG;oCACzD,eAAe,IAAI,aAAa,IAAI,EAAE;gCACxC,CAAC;;wBAEH,iBAAiB,WAAW,aAAa,IAAI;wBAC7C,gBAAgB,WAAW,YAAY,IAAI,EAAE;wBAC7C,mBAAmB,WAAW,eAAe,KAAK,YAAY,WAAW,eAAe,GAAG;oBAC7F,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iDAAiD;wBAC/D,qCAAqC;wBACrC,eAAe;wBACf,cAAc,EAAE;wBAChB,iBAAiB;wBACjB,gBAAgB,EAAE;wBAClB,mBAAmB;oBACrB;gBACF,OAAO;oBACL,0CAA0C;oBAC1C,eAAe;oBACf,cAAc,EAAE;oBAChB,iBAAiB;oBACjB,gBAAgB,EAAE;oBAClB,mBAAmB;gBACrB;YACF;QACF;+BAAG;QAAC;QAAa;QAAa;QAAQ;QAAW;KAAmB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,aAAa;YACnB,IAAI,CAAC,cAAc,CAAC,aAAa,eAAe,CAAC,aAAa,QAAQ,0DAA0D;YAEhI,MAAM,aAAyB;gBAAE;gBAAa;gBAAY;gBAAe;gBAAc;YAAgB;YACvG,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAEhD,IAAI,cAAc,KAAK,WAAW,MAAM,GAAG,GAAG;gBAC5C,iBAAiB,gBAAgB,cAAc;YACjD;YACA,IAAI,eAAe,cAAc;gBAC/B,iBAAiB,gBAAgB,YAAY;YAC/C;QACF;+BAAG;QAAC;QAAa;QAAY;QAAe;QAAc;QAAiB;QAAW;QAAkB;QAAoB;QAAa;KAAY;IAErJ,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YAC3C;oEAAmB,CAAA,OAAQ,CAAC;;QAC9B;2DAAG,EAAE;IAEL,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YAC5C;oEAAc,CAAA,iBACZ,eAAe,GAAG;4EAAC,CAAA,MACjB,IAAI,EAAE,KAAK,aAAa;gCAAE,GAAG,GAAG;gCAAE,WAAW,CAAC,CAAC,IAAI,SAAS,IAAI,IAAI;4BAAE,IAAI;;;QAGhF;2DAAG,EAAE;IAIL,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,CAAC,MAAc;YAC7C,MAAM,cAAwB;gBAC5B,IAAI,OAAO,UAAU;gBACrB;gBACA;gBACA,eAAe,EAAE;gBACjB,WAAW;YACb;YACA;uDAAc,CAAC,OAAS;2BAAI;wBAAM;qBAAY;;QAChD;8CAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,IAAY,SAAiB;YAC/D;0DAAc,CAAC,OACb,KAAK,GAAG;kEAAC,CAAC,MACR,IAAI,EAAE,KAAK,KAAK;gCAAE,GAAG,GAAG;gCAAE,MAAM;gCAAS,QAAQ;4BAAU,IAAI;;;QAGrE;iDAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAClC;0DAAc,CAAC,OAAS,KAAK,MAAM;kEAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;;;QAC1D;iDAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,UAAkB,MAAc;YAClE,IAAI,UAAU;YACd;0DAAc,CAAC,OACb,KAAK,GAAG;kEAAC,CAAC;4BACR,IAAI,IAAI,EAAE,KAAK,UAAU;gCACvB,MAAM,kBAAkB,IAAI,aAAa,CAAC,MAAM;8FAAC,CAAC,KAAK,KAAO,MAAM,GAAG,eAAe;6FAAE;gCACxF,IAAI,kBAAkB,kBAAkB,IAAI,MAAM,EAAE;oCAClD,UAAU;oCACV,OAAO;gCACT;gCACA,MAAM,iBAA8B;oCAClC,IAAI,OAAO,UAAU;oCACrB;oCACA;gCACF;gCACA,UAAU;gCACV,OAAO;oCAAE,GAAG,GAAG;oCAAE,eAAe;2CAAI,IAAI,aAAa;wCAAE;qCAAe;gCAAC;4BACzE;4BACA,OAAO;wBACT;;;YAEF,OAAO;QACT;iDAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC,UAAkB,OAAe,SAAiB;YACvF,IAAI,UAAU;YACd;6DAAc,CAAC,OACb,KAAK,GAAG;qEAAC,CAAC;4BACR,IAAI,IAAI,EAAE,KAAK,UAAU;gCACvB,MAAM,0BAA0B,IAAI,aAAa,CAC9C,MAAM;yGAAC,CAAA,KAAM,GAAG,EAAE,KAAK;wGACvB,MAAM;yGAAC,CAAC,KAAK,KAAO,MAAM,GAAG,eAAe;wGAAE;gCAEjD,IAAI,0BAA0B,qBAAqB,IAAI,MAAM,EAAE;oCAC7D,UAAU;oCACV,OAAO;gCACT;gCACA,UAAU;gCACV,OAAO;oCACL,GAAG,GAAG;oCACN,eAAe,IAAI,aAAa,CAAC,GAAG;qFAAC,CAAC,KACpC,GAAG,EAAE,KAAK,QAAQ;gDAAE,GAAG,EAAE;gDAAE,MAAM;gDAAS,iBAAiB;4CAAmB,IAAI;;gCAEtF;4BACF;4BACA,OAAO;wBACT;;;YAEF,OAAO;QACT;oDAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC,UAAkB;YACvD;6DAAc,CAAC,OACb,KAAK,GAAG;qEAAC,CAAC,MACR,IAAI,EAAE,KAAK,WACP;gCAAE,GAAG,GAAG;gCAAE,eAAe,IAAI,aAAa,CAAC,MAAM;iFAAC,CAAC,KAAO,GAAG,EAAE,KAAK;;4BAAO,IAC3E;;;YAGR,MAAM;gBAAE,OAAO;gBAAuB,aAAa;YAA+B;QACpF;oDAAG;QAAC;KAAM;IAEV,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC,MAAc,cAAsB;YAC9E,MAAM,UAAyB;gBAC7B,IAAI,eAAe,MAAM,OAAO,UAAU;gBAC1C;gBACA;gBACA,aAAa,eAAe,KAAK,cAAc,WAAW,GAAG;gBAC7D;gBACA,SAAS,eAAe,WAAW,IAAI,OAAO,WAAW;gBACzD,cAAc;YAChB;YACA,iBAAiB;YACjB,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAgB,WAAW,KAAK,CAAC,eAAe;gBACvE,iBAAiB,gBAAgB,WAAW;YAChD;YACA,MAAM;gBAAE,OAAO;gBAA2B,aAAa,CAAC,WAAW,EAAE,KAAK,uBAAuB,CAAC;YAAA;QACpG;yDAAG;QAAC;QAAe;QAAkB;QAAO;KAAa;IAEzD,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YAC5C,IAAI,eAAe;gBACjB,MAAM,cAAc;oBAAE,GAAG,aAAa;oBAAE;gBAAY;gBACpD,IAAI,eAAe,cAAc,YAAY,IAAI,CAAC,cAAc,YAAY,EAAE;oBAC5E,YAAY,YAAY,GAAG,IAAI,OAAO,WAAW;oBACjD,iBAAiB,gBAAgB,YAAY;oBAC7C,MAAM;wBAAE,OAAO;wBAAkB,aAAa,CAAC,uCAAuC,EAAE,cAAc,IAAI,CAAC,CAAC,CAAC;wBAAE,UAAU;oBAAK;gBAChI;gBACA,iBAAiB;YACnB;QACF;2DAAG;QAAC;QAAe;QAAkB;KAAM;IAE3C,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAClC,iBAAiB;YACjB,MAAM;gBAAE,OAAO;gBAA0B,aAAa;YAAuC;QAC/F;kDAAG;QAAC;KAAM;IAEV,MAAM,wBAAwB,WAAW,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,MAAM,EAAE;IAChF,MAAM,mBAAmB,cAAc;IAEvC,IAAI,eAAe,CAAC,aAAa,CAAC,aAAa;QAC7C,qBACE,6LAAC,+IAAA,CAAA,UAAY;YACX,iBAAiB;YACjB,kBAAkB;sBAElB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,QAAQ;4BAAI;4BACvB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;gCAAU,MAAM;4BAAS;;;;;;sCAE9D,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAK/C;IAEA,qBACE,6LAAC,+IAAA,CAAA,UAAY;QACX,iBAAiB;QACjB,kBAAkB;;0BAElB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAOvC,6LAAC,sJAAA,CAAA,iBAAc;wBACb,aAAa;wBACb,gBAAgB;wBAChB,WAAW;wBACX,aAAa,eAAe;wBAC5B,iBAAiB;;;;;;kCAInB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,kJAAA,CAAA,eAAY;wCAAC,OAAO;wCAAK,WAAU;;0DAClC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAwB;;;;;;kFACtC,6LAAC;wEAAE,WAAU;kFAAgC;;;;;;;;;;;;;;;;;;kEAGjD,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAK,WAAU;;0EAC1C,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAI3C,6LAAC,mJAAA,CAAA,UAAgB;gDACf,aAAa;gDACb,YAAY;gDACZ,iBAAiB;;;;;;;;;;;;kDAKrB,6LAAC,kJAAA,CAAA,eAAY;wCAAC,OAAO;wCAAK,WAAU;kDAClC,cAAA,6LAAC,kJAAA,CAAA,UAAe;4CACd,YAAY;4CACZ,eAAe;4CACf,kBAAkB;4CAClB,kBAAkB;4CAClB,kBAAkB;4CAClB,qBAAqB;4CACrB,qBAAqB;4CACrB,4BAA4B;4CAC5B,iBAAiB;;;;;;;;;;;;;;;;;0CAMvB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,kJAAA,CAAA,eAAY;wCAAC,OAAO;wCAAK,WAAU;kDAClC,cAAA,6LAAC,uJAAA,CAAA,UAAoB;4CACnB,MAAM;4CACN,WAAW;4CACX,kBAAkB;4CAClB,aAAa;4CACb,kBAAkB;4CAClB,iBAAiB;;;;;;;;;;;kDAKrB,6LAAC,kJAAA,CAAA,eAAY;wCAAC,OAAO;wCAAK,WAAU;;0DAClC,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAyB;;;;;;;;;;;;0DAI/C,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,WAAU;;0EAClC,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGnC,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,WAAU;;0EAClC,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGrC,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,WAAU;;0EAClC,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;kDAOzC,6LAAC,kJAAA,CAAA,eAAY;wCAAC,OAAO;wCAAK,WAAU;;0DAClC,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAyB;;;;;;;;;;;;0DAInD,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAsB;;;;;;sFACnC,6LAAC;4EAAE,WAAU;sFAAgC;;;;;;;;;;;;;;;;;;sEAGjD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAsB;;;;;;sFACnC,6LAAC;4EAAE,WAAU;sFAAgC;;;;;;;;;;;;;;;;;;sEAGjD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAsB;;;;;;sFACnC,6LAAC;4EAAE,WAAU;sFAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW7D,6LAAC,2JAAA,CAAA,UAAoB;;;;;;;;;;;AAG3B;GApawB;;QACwB,iIAAA,CAAA,UAAO;QACtC,qIAAA,CAAA,YAAS;QAQN,+HAAA,CAAA,WAAQ;;;KAVJ", "debugId": null}}]}