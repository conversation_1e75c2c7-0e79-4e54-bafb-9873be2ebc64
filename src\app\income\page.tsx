'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Plus, Wallet, TrendingUp, Edit, Trash2, DollarSign } from 'lucide-react';
import ModernLayout from '@/components/layout/ModernLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AnimatedCard } from '@/components/ui/animated-wrapper';
import { useToast } from '@/hooks/use-toast';

interface IncomeSource {
  id: string;
  name: string;
  amount: number;
  frequency: 'monthly' | 'weekly' | 'yearly';
  category: string;
  isActive: boolean;
}

export default function IncomePage() {
  const [incomeSources, setIncomeSources] = useState<IncomeSource[]>([
    { id: '1', name: 'Primary Salary', amount: 5000, frequency: 'monthly', category: 'Employment', isActive: true },
    { id: '2', name: 'Freelance Work', amount: 800, frequency: 'monthly', category: 'Freelance', isActive: true },
    { id: '3', name: 'Investment Returns', amount: 200, frequency: 'monthly', category: 'Investment', isActive: true },
  ]);
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingIncome, setEditingIncome] = useState<IncomeSource | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    amount: '',
    frequency: 'monthly' as const,
    category: '',
  });
  
  const { toast } = useToast();

  const totalMonthlyIncome = incomeSources
    .filter(source => source.isActive)
    .reduce((total, source) => {
      const monthlyAmount = source.frequency === 'yearly' 
        ? source.amount / 12 
        : source.frequency === 'weekly' 
        ? source.amount * 4.33 
        : source.amount;
      return total + monthlyAmount;
    }, 0);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.amount || !formData.category) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    const newIncome: IncomeSource = {
      id: editingIncome?.id || Date.now().toString(),
      name: formData.name,
      amount: parseFloat(formData.amount),
      frequency: formData.frequency,
      category: formData.category,
      isActive: true,
    };

    if (editingIncome) {
      setIncomeSources(prev => prev.map(income => 
        income.id === editingIncome.id ? newIncome : income
      ));
      toast({
        title: "Success",
        description: "Income source updated successfully",
      });
    } else {
      setIncomeSources(prev => [...prev, newIncome]);
      toast({
        title: "Success",
        description: "Income source added successfully",
      });
    }

    setFormData({ name: '', amount: '', frequency: 'monthly', category: '' });
    setEditingIncome(null);
    setIsDialogOpen(false);
  };

  const handleEdit = (income: IncomeSource) => {
    setEditingIncome(income);
    setFormData({
      name: income.name,
      amount: income.amount.toString(),
      frequency: income.frequency,
      category: income.category,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (id: string) => {
    setIncomeSources(prev => prev.filter(income => income.id !== id));
    toast({
      title: "Success",
      description: "Income source deleted successfully",
    });
  };

  const getFrequencyMultiplier = (frequency: string) => {
    switch (frequency) {
      case 'yearly': return 1/12;
      case 'weekly': return 4.33;
      default: return 1;
    }
  };

  return (
    <ModernLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <motion.div
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div>
            <h1 className="text-3xl font-bold text-foreground">Income Management</h1>
            <p className="text-muted-foreground">
              Track and manage your income sources
            </p>
          </div>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="btn-modern-primary">
                <Plus className="w-4 h-4 mr-2" />
                Add Income Source
              </Button>
            </DialogTrigger>
            <DialogContent className="glass-card border-white/20">
              <DialogHeader>
                <DialogTitle>
                  {editingIncome ? 'Edit Income Source' : 'Add New Income Source'}
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="name">Income Source Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Primary Salary, Freelance Work"
                    className="input-modern"
                  />
                </div>
                <div>
                  <Label htmlFor="amount">Amount</Label>
                  <Input
                    id="amount"
                    type="number"
                    value={formData.amount}
                    onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                    placeholder="0.00"
                    className="input-modern"
                  />
                </div>
                <div>
                  <Label htmlFor="frequency">Frequency</Label>
                  <Select value={formData.frequency} onValueChange={(value: any) => setFormData(prev => ({ ...prev, frequency: value }))}>
                    <SelectTrigger className="input-modern">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="yearly">Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    placeholder="e.g., Employment, Freelance, Investment"
                    className="input-modern"
                  />
                </div>
                <div className="flex gap-2 pt-4">
                  <Button type="submit" className="btn-modern-primary flex-1">
                    {editingIncome ? 'Update' : 'Add'} Income Source
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => {
                      setIsDialogOpen(false);
                      setEditingIncome(null);
                      setFormData({ name: '', amount: '', frequency: 'monthly', category: '' });
                    }}
                    className="glass-card border-white/20"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </motion.div>

        {/* Income Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <AnimatedCard delay={0.1} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Monthly Income</p>
                <p className="text-2xl font-bold text-foreground">R {totalMonthlyIncome.toFixed(2)}</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.2} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-success/20 to-primary/20 rounded-xl flex items-center justify-center">
                <Wallet className="w-6 h-6 text-success" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Active Sources</p>
                <p className="text-2xl font-bold text-foreground">{incomeSources.filter(s => s.isActive).length}</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.3} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-accent/20 to-success/20 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-accent" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Yearly Projection</p>
                <p className="text-2xl font-bold text-foreground">R {(totalMonthlyIncome * 12).toFixed(2)}</p>
              </div>
            </div>
          </AnimatedCard>
        </div>

        {/* Income Sources List */}
        <AnimatedCard delay={0.4} className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wallet className="w-5 h-5 text-primary" />
              Income Sources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {incomeSources.map((income, index) => (
                <motion.div
                  key={income.id}
                  className="flex items-center justify-between p-4 rounded-xl bg-white/5 hover:bg-white/10 transition-colors"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="flex-1">
                    <h3 className="font-semibold text-foreground">{income.name}</h3>
                    <p className="text-sm text-muted-foreground">{income.category}</p>
                  </div>
                  <div className="text-right mr-4">
                    <p className="font-semibold text-foreground">R {income.amount.toFixed(2)}</p>
                    <p className="text-sm text-muted-foreground capitalize">{income.frequency}</p>
                    <p className="text-xs text-accent">
                      R {(income.amount * getFrequencyMultiplier(income.frequency)).toFixed(2)}/month
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEdit(income)}
                      className="glass-card w-8 h-8"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDelete(income.id)}
                      className="glass-card w-8 h-8 hover:bg-destructive/20"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </motion.div>
              ))}
              
              {incomeSources.length === 0 && (
                <div className="text-center py-8">
                  <Wallet className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No income sources added yet</p>
                  <p className="text-sm text-muted-foreground">Click "Add Income Source" to get started</p>
                </div>
              )}
            </div>
          </CardContent>
        </AnimatedCard>
      </div>
    </ModernLayout>
  );
}
