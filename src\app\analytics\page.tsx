'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { BarChart3, TrendingUp, TrendingDown, Calendar, Filter, Download } from 'lucide-react';
import ModernLayout from '@/components/layout/ModernLayout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AnimatedCard } from '@/components/ui/animated-wrapper';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

const monthlyData = [
  { month: 'Jan', income: 6000, expenses: 4500, savings: 1500 },
  { month: 'Feb', income: 6200, expenses: 4800, savings: 1400 },
  { month: 'Mar', income: 6000, expenses: 4200, savings: 1800 },
  { month: 'Apr', income: 6500, expenses: 5100, savings: 1400 },
  { month: 'May', income: 6300, expenses: 4900, savings: 1400 },
  { month: 'Jun', income: 6800, expenses: 5200, savings: 1600 },
];

const categoryData = [
  { name: 'Food & Dining', value: 1200, color: '#10B981' },
  { name: 'Transportation', value: 800, color: '#3B82F6' },
  { name: 'Entertainment', value: 400, color: '#8B5CF6' },
  { name: 'Shopping', value: 600, color: '#F59E0B' },
  { name: 'Bills & Utilities', value: 1000, color: '#EF4444' },
];

const weeklySpending = [
  { week: 'Week 1', amount: 1200 },
  { week: 'Week 2', amount: 980 },
  { week: 'Week 3', amount: 1450 },
  { week: 'Week 4', amount: 1100 },
];

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState('6months');
  const [chartType, setChartType] = useState('overview');

  const totalIncome = monthlyData.reduce((sum, month) => sum + month.income, 0);
  const totalExpenses = monthlyData.reduce((sum, month) => sum + month.expenses, 0);
  const totalSavings = totalIncome - totalExpenses;
  const savingsRate = (totalSavings / totalIncome) * 100;

  const currentMonth = monthlyData[monthlyData.length - 1];
  const previousMonth = monthlyData[monthlyData.length - 2];
  const incomeChange = ((currentMonth.income - previousMonth.income) / previousMonth.income) * 100;
  const expenseChange = ((currentMonth.expenses - previousMonth.expenses) / previousMonth.expenses) * 100;

  return (
    <ModernLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <motion.div
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div>
            <h1 className="text-3xl font-bold text-foreground">Financial Analytics</h1>
            <p className="text-muted-foreground">
              Insights and trends from your financial data
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="input-modern w-40">
                <Calendar className="w-4 h-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1month">Last Month</SelectItem>
                <SelectItem value="3months">Last 3 Months</SelectItem>
                <SelectItem value="6months">Last 6 Months</SelectItem>
                <SelectItem value="1year">Last Year</SelectItem>
              </SelectContent>
            </Select>
            
            <Button variant="outline" className="glass-card border-white/20">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </motion.div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <AnimatedCard delay={0.1} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Income</p>
                <p className="text-xl font-bold text-foreground">R {totalIncome.toLocaleString()}</p>
                <p className={`text-xs flex items-center gap-1 ${incomeChange >= 0 ? 'text-success' : 'text-destructive'}`}>
                  {incomeChange >= 0 ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                  {Math.abs(incomeChange).toFixed(1)}% vs last month
                </p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.2} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-warning/20 to-destructive/20 rounded-xl flex items-center justify-center">
                <TrendingDown className="w-6 h-6 text-warning" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Expenses</p>
                <p className="text-xl font-bold text-foreground">R {totalExpenses.toLocaleString()}</p>
                <p className={`text-xs flex items-center gap-1 ${expenseChange <= 0 ? 'text-success' : 'text-destructive'}`}>
                  {expenseChange <= 0 ? <TrendingDown className="w-3 h-3" /> : <TrendingUp className="w-3 h-3" />}
                  {Math.abs(expenseChange).toFixed(1)}% vs last month
                </p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.3} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-success/20 to-primary/20 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-success" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Savings</p>
                <p className="text-xl font-bold text-foreground">R {totalSavings.toLocaleString()}</p>
                <p className="text-xs text-success">
                  {savingsRate.toFixed(1)}% savings rate
                </p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.4} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-accent/20 to-success/20 rounded-xl flex items-center justify-center">
                <BarChart3 className="w-6 h-6 text-accent" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Avg Monthly</p>
                <p className="text-xl font-bold text-foreground">R {(totalExpenses / monthlyData.length).toFixed(0)}</p>
                <p className="text-xs text-muted-foreground">
                  Monthly expenses
                </p>
              </div>
            </div>
          </AnimatedCard>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Income vs Expenses Trend */}
          <AnimatedCard delay={0.5} className="chart-container">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-primary" />
                Income vs Expenses Trend
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="month" stroke="rgba(255,255,255,0.6)" />
                  <YAxis stroke="rgba(255,255,255,0.6)" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: 'rgba(0,0,0,0.8)', 
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px'
                    }} 
                  />
                  <Line type="monotone" dataKey="income" stroke="#10B981" strokeWidth={3} />
                  <Line type="monotone" dataKey="expenses" stroke="#EF4444" strokeWidth={3} />
                  <Line type="monotone" dataKey="savings" stroke="#3B82F6" strokeWidth={3} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </AnimatedCard>

          {/* Category Breakdown */}
          <AnimatedCard delay={0.6} className="chart-container">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-primary" />
                Spending by Category
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={categoryData}
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {categoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: 'rgba(0,0,0,0.8)', 
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px'
                    }} 
                  />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </AnimatedCard>

          {/* Weekly Spending */}
          <AnimatedCard delay={0.7} className="chart-container">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-primary" />
                Weekly Spending Pattern
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={weeklySpending}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="week" stroke="rgba(255,255,255,0.6)" />
                  <YAxis stroke="rgba(255,255,255,0.6)" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: 'rgba(0,0,0,0.8)', 
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px'
                    }} 
                  />
                  <Bar dataKey="amount" fill="#8B5CF6" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </AnimatedCard>

          {/* Financial Health Score */}
          <AnimatedCard delay={0.8} className="chart-container">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-primary" />
                Financial Health Score
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <div className="text-4xl font-bold text-success mb-2">85</div>
                <p className="text-muted-foreground">Excellent financial health</p>
              </div>
              
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Savings Rate</span>
                    <span className="text-success">Good</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-success h-2 rounded-full" style={{ width: '75%' }}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Budget Adherence</span>
                    <span className="text-warning">Fair</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-warning h-2 rounded-full" style={{ width: '60%' }}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Income Stability</span>
                    <span className="text-success">Excellent</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-success h-2 rounded-full" style={{ width: '90%' }}></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </AnimatedCard>
        </div>
      </div>
    </ModernLayout>
  );
}
