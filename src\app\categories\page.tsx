'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Plus, <PERSON><PERSON><PERSON>, Edit, Trash2, Target, TrendingUp, AlertCircle } from 'lucide-react';
import ModernLayout from '@/components/layout/ModernLayout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { AnimatedCard } from '@/components/ui/animated-wrapper';
import { useToast } from '@/hooks/use-toast';

interface Category {
  id: string;
  name: string;
  budget: number;
  spent: number;
  color: string;
  icon: string;
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([
    { id: '1', name: 'Food & Dining', budget: 1200, spent: 850, color: '#10B981', icon: '🍽️' },
    { id: '2', name: 'Transportation', budget: 800, spent: 650, color: '#3B82F6', icon: '🚗' },
    { id: '3', name: 'Entertainment', budget: 400, spent: 480, color: '#8B5CF6', icon: '🎬' },
    { id: '4', name: 'Shopping', budget: 600, spent: 320, color: '#F59E0B', icon: '🛍️' },
    { id: '5', name: 'Bills & Utilities', budget: 1000, spent: 950, color: '#EF4444', icon: '⚡' },
  ]);
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    budget: '',
    color: '#10B981',
    icon: '📝',
  });
  
  const { toast } = useToast();

  const totalBudget = categories.reduce((sum, cat) => sum + cat.budget, 0);
  const totalSpent = categories.reduce((sum, cat) => sum + cat.spent, 0);
  const overBudgetCategories = categories.filter(cat => cat.spent > cat.budget);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.budget) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    const newCategory: Category = {
      id: editingCategory?.id || Date.now().toString(),
      name: formData.name,
      budget: parseFloat(formData.budget),
      spent: editingCategory?.spent || 0,
      color: formData.color,
      icon: formData.icon,
    };

    if (editingCategory) {
      setCategories(prev => prev.map(cat => 
        cat.id === editingCategory.id ? newCategory : cat
      ));
      toast({
        title: "Success",
        description: "Category updated successfully",
      });
    } else {
      setCategories(prev => [...prev, newCategory]);
      toast({
        title: "Success",
        description: "Category added successfully",
      });
    }

    setFormData({ name: '', budget: '', color: '#10B981', icon: '📝' });
    setEditingCategory(null);
    setIsDialogOpen(false);
  };

  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      budget: category.budget.toString(),
      color: category.color,
      icon: category.icon,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (id: string) => {
    setCategories(prev => prev.filter(cat => cat.id !== id));
    toast({
      title: "Success",
      description: "Category deleted successfully",
    });
  };

  const getProgressColor = (spent: number, budget: number) => {
    const percentage = (spent / budget) * 100;
    if (percentage > 100) return 'bg-destructive';
    if (percentage > 80) return 'bg-warning';
    return 'bg-success';
  };

  return (
    <ModernLayout>
      <div className="space-y-8">
        {/* Page Header */}
        <motion.div
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div>
            <h1 className="text-3xl font-bold text-foreground">Budget Categories</h1>
            <p className="text-muted-foreground">
              Manage your spending categories and budgets
            </p>
          </div>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="btn-modern-primary">
                <Plus className="w-4 h-4 mr-2" />
                Add Category
              </Button>
            </DialogTrigger>
            <DialogContent className="glass-card border-white/20">
              <DialogHeader>
                <DialogTitle>
                  {editingCategory ? 'Edit Category' : 'Add New Category'}
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="name">Category Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Food & Dining, Transportation"
                    className="input-modern"
                  />
                </div>
                <div>
                  <Label htmlFor="budget">Monthly Budget</Label>
                  <Input
                    id="budget"
                    type="number"
                    value={formData.budget}
                    onChange={(e) => setFormData(prev => ({ ...prev, budget: e.target.value }))}
                    placeholder="0.00"
                    className="input-modern"
                  />
                </div>
                <div>
                  <Label htmlFor="icon">Icon (Emoji)</Label>
                  <Input
                    id="icon"
                    value={formData.icon}
                    onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
                    placeholder="📝"
                    className="input-modern"
                  />
                </div>
                <div>
                  <Label htmlFor="color">Color</Label>
                  <div className="flex gap-2 items-center">
                    <input
                      type="color"
                      id="color"
                      value={formData.color}
                      onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                      className="w-12 h-10 rounded-lg border border-white/20 bg-transparent"
                    />
                    <Input
                      value={formData.color}
                      onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                      placeholder="#10B981"
                      className="input-modern flex-1"
                    />
                  </div>
                </div>
                <div className="flex gap-2 pt-4">
                  <Button type="submit" className="btn-modern-primary flex-1">
                    {editingCategory ? 'Update' : 'Add'} Category
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => {
                      setIsDialogOpen(false);
                      setEditingCategory(null);
                      setFormData({ name: '', budget: '', color: '#10B981', icon: '📝' });
                    }}
                    className="glass-card border-white/20"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </motion.div>

        {/* Budget Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <AnimatedCard delay={0.1} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl flex items-center justify-center">
                <Target className="w-6 h-6 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Budget</p>
                <p className="text-2xl font-bold text-foreground">R {totalBudget.toFixed(2)}</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.2} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-success/20 to-primary/20 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-success" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Spent</p>
                <p className="text-2xl font-bold text-foreground">R {totalSpent.toFixed(2)}</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard delay={0.3} className="metric-card">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-warning/20 to-destructive/20 rounded-xl flex items-center justify-center">
                <AlertCircle className="w-6 h-6 text-warning" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Over Budget</p>
                <p className="text-2xl font-bold text-foreground">{overBudgetCategories.length}</p>
              </div>
            </div>
          </AnimatedCard>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category, index) => {
            const percentage = (category.spent / category.budget) * 100;
            const isOverBudget = category.spent > category.budget;
            
            return (
              <AnimatedCard key={category.id} delay={index * 0.1} className="glass-card">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-10 h-10 rounded-xl flex items-center justify-center text-lg"
                        style={{ backgroundColor: `${category.color}20` }}
                      >
                        {category.icon}
                      </div>
                      <div>
                        <CardTitle className="text-lg">{category.name}</CardTitle>
                        <p className="text-sm text-muted-foreground">
                          R {category.spent.toFixed(2)} / R {category.budget.toFixed(2)}
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(category)}
                        className="glass-card w-8 h-8"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(category.id)}
                        className="glass-card w-8 h-8 hover:bg-destructive/20"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className={isOverBudget ? 'text-destructive' : 'text-muted-foreground'}>
                        {percentage.toFixed(1)}% used
                      </span>
                      <span className={isOverBudget ? 'text-destructive font-semibold' : 'text-muted-foreground'}>
                        {isOverBudget ? `R ${(category.spent - category.budget).toFixed(2)} over` : `R ${(category.budget - category.spent).toFixed(2)} left`}
                      </span>
                    </div>
                    <Progress 
                      value={Math.min(percentage, 100)} 
                      className="progress-modern h-3"
                    />
                    {isOverBudget && (
                      <div className="flex items-center gap-2 text-destructive text-sm">
                        <AlertCircle className="w-4 h-4" />
                        <span>Over budget!</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </AnimatedCard>
            );
          })}
        </div>

        {categories.length === 0 && (
          <AnimatedCard delay={0.4} className="glass-card">
            <CardContent className="text-center py-12">
              <PieChart className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-foreground mb-2">No categories yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first budget category to start tracking your expenses
              </p>
              <Button 
                onClick={() => setIsDialogOpen(true)}
                className="btn-modern-primary"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Category
              </Button>
            </CardContent>
          </AnimatedCard>
        )}
      </div>
    </ModernLayout>
  );
}
